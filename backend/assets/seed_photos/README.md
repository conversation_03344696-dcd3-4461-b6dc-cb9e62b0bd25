# Seed Photos

This directory contains seed photos for testing and development purposes. The photos are automatically generated using the Picsum Photos API.

## Photo Specifications

- **Dimensions**: 1080x1920 (mobile portrait format)
- **Format**: JPEG
- **Naming**: Sequential numbering (1.jpg, 2.jpg, 3.jpg, etc.)
- **Source**: [Picsum Photos](https://picsum.photos) - <PERSON><PERSON> for photos

## Generating Photos

### Using npm scripts (recommended):

```bash
# Generate default number of photos (50)
npm run seed:photos

# View help and options
npm run seed:photos:help
```

### Using the script directly:

```bash
# Generate default number of photos (50)
node scripts/generate-seed-photos.js

# Generate specific number of photos
node scripts/generate-seed-photos.js --count 100

# View help
node scripts/generate-seed-photos.js --help
```

## How it works

1. The script checks the current directory for existing photos
2. It detects any gaps in the sequence (e.g., missing 2.jpg, 5.jpg, etc.)
3. It fills in gaps first, then adds new photos at the end if needed
4. It only downloads photos if needed to reach the target count
5. Each photo is downloaded from `https://picsum.photos/1080/1920`
6. Photos are saved with sequential numbering

## Examples

```bash
# If you have no photos and run with --count 10:
# Downloads: 1.jpg, 2.jpg, 3.jpg, ..., 10.jpg

# If you have 1.jpg, 3.jpg, 5.jpg and run with --count 10:
# Downloads: 2.jpg, 4.jpg, 6.jpg, 7.jpg, 8.jpg, 9.jpg, 10.jpg (fills gaps first)

# If you already have 1.jpg through 5.jpg and run with --count 10:
# Downloads: 6.jpg, 7.jpg, 8.jpg, 9.jpg, 10.jpg

# If you have 15 photos with some gaps and run with --count 10:
# Downloads only the missing photos to fill gaps (if any)

# If you already have 15 complete photos and run with --count 10:
# No downloads (already have enough)
```

## Notes

- The script includes a 500ms delay between downloads to be respectful to the Picsum Photos API
- Failed downloads are reported and the script can be re-run to retry failed photos
- The script automatically detects and fills gaps in the photo sequence
- All photos are unique random images from Picsum Photos
- The script is safe to run multiple times - it won't re-download existing photos
- Gap detection ensures a complete sequence from 1.jpg to your target count
