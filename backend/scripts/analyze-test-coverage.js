#!/usr/bin/env node

/**
 * API Route Coverage Analysis Script
 * 
 * This script analyzes test coverage by cross-referencing all API routes
 * with existing test files to identify coverage gaps.
 */

const fs = require('fs');
const path = require('path');

// Load API documentation
const apiDocsPath = path.join(__dirname, '../../.kiro/api-docs/api-documentation.json');
const testsDir = path.join(__dirname, '../tests');

function loadApiDocumentation() {
    try {
        const apiDocs = JSON.parse(fs.readFileSync(apiDocsPath, 'utf8'));
        return apiDocs.routes || [];
    } catch (error) {
        console.error('Error loading API documentation:', error.message);
        return [];
    }
}

function getExistingTestFiles() {
    try {
        const files = fs.readdirSync(testsDir);
        return files.filter(file => file.endsWith('.test.ts') || file.endsWith('.test.js'));
    } catch (error) {
        console.error('Error reading test directory:', error.message);
        return [];
    }
}

function analyzeTestFile(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');

        // Extract API routes being tested
        const routePatterns = [
            /['"`]\/[^'"`\s]+['"`]/g,  // String literals with paths
            /\.get\(['"`]([^'"`]+)['"`]\)/g,  // HTTP GET calls
            /\.post\(['"`]([^'"`]+)['"`]\)/g, // HTTP POST calls
            /\.put\(['"`]([^'"`]+)['"`]\)/g,  // HTTP PUT calls
            /\.delete\(['"`]([^'"`]+)['"`]\)/g, // HTTP DELETE calls
        ];

        const testedRoutes = new Set();

        routePatterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(content)) !== null) {
                const route = match[1] || match[0].replace(/['"`]/g, '');
                if (route.startsWith('/')) {
                    testedRoutes.add(route);
                }
            }
        });

        // Extract HTTP methods being tested
        const methodPatterns = [
            /describe\(['"`]([^'"`]*(?:GET|POST|PUT|DELETE)[^'"`]*)['"`]/gi,
            /it\(['"`]([^'"`]*(?:GET|POST|PUT|DELETE)[^'"`]*)['"`]/gi,
            /test\(['"`]([^'"`]*(?:GET|POST|PUT|DELETE)[^'"`]*)['"`]/gi,
        ];

        const testedMethods = new Set();
        methodPatterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(content)) !== null) {
                const description = match[1].toUpperCase();
                ['GET', 'POST', 'PUT', 'DELETE'].forEach(method => {
                    if (description.includes(method)) {
                        testedMethods.add(method);
                    }
                });
            }
        });

        return {
            routes: Array.from(testedRoutes),
            methods: Array.from(testedMethods),
            hasAuthTests: /auth|token|signin|signup|login/i.test(content),
            hasErrorTests: /error|fail|invalid|unauthorized|forbidden/i.test(content),
            hasValidationTests: /validat|schema|required|missing/i.test(content),
            lineCount: content.split('\n').length
        };
    } catch (error) {
        console.error(`Error analyzing test file ${filePath}:`, error.message);
        return {
            routes: [],
            methods: [],
            hasAuthTests: false,
            hasErrorTests: false,
            hasValidationTests: false,
            lineCount: 0
        };
    }
}

function categorizeRoutes(routes) {
    const categories = {
        auth: [],
        users: [],
        posts: [],
        channels: [],
        media: [],
        notifications: [],
        reflexes: [],
        health: [],
        xbox: [],
        twitch: [],
        kick: []
    };

    routes.forEach(route => {
        const path = route.path.toLowerCase();
        if (path.startsWith('/auth')) categories.auth.push(route);
        else if (path.startsWith('/users')) categories.users.push(route);
        else if (path.startsWith('/posts')) categories.posts.push(route);
        else if (path.startsWith('/channels')) categories.channels.push(route);
        else if (path.startsWith('/media')) categories.media.push(route);
        else if (path.includes('notification') || path.startsWith('/device-token')) categories.notifications.push(route);
        else if (path.startsWith('/reflexes')) categories.reflexes.push(route);
        else if (path.startsWith('/health')) categories.health.push(route);
        else if (path.startsWith('/xbox')) categories.xbox.push(route);
        else if (path.startsWith('/twitch')) categories.twitch.push(route);
        else if (path.startsWith('/kick')) categories.kick.push(route);
    });

    return categories;
}

function generateCoverageReport() {
    console.log('INFO: Analyzing API Route Test Coverage...');

    const routes = loadApiDocumentation();
    const testFiles = getExistingTestFiles();

    console.log(`INFO: Found ${routes.length} API routes and ${testFiles.length} test files`);

    // Analyze existing test files
    const testAnalysis = {};
    testFiles.forEach(file => {
        const filePath = path.join(testsDir, file);
        testAnalysis[file] = analyzeTestFile(filePath);
    });

    // Categorize routes
    const routeCategories = categorizeRoutes(routes);

    // Generate coverage analysis
    const coverage = {
        total: routes.length,
        covered: 0,
        uncovered: [],
        partialCoverage: [],
        fullCoverage: [],
        missingTestFiles: [],
        gapsByCategory: {}
    };

    // Analyze coverage by category
    Object.entries(routeCategories).forEach(([category, categoryRoutes]) => {
        const testFile = `${category}.test.ts`;
        const hasTestFile = testFiles.includes(testFile);

        coverage.gapsByCategory[category] = {
            routes: categoryRoutes,
            hasTestFile,
            testFile,
            coverage: hasTestFile ? 'partial' : 'none',
            gaps: []
        };

        if (!hasTestFile && categoryRoutes.length > 0) {
            coverage.missingTestFiles.push({
                category,
                testFile,
                routeCount: categoryRoutes.length,
                routes: categoryRoutes
            });
        }

        // Analyze specific route coverage
        categoryRoutes.forEach(route => {
            const routeKey = `${route.method} ${route.path}`;
            let isCovered = false;

            if (hasTestFile) {
                const analysis = testAnalysis[testFile];
                if (analysis) {
                    // Check if route path is mentioned in tests
                    const pathCovered = analysis.routes.some(testedRoute =>
                        route.path.includes(testedRoute) || testedRoute.includes(route.path.split('/')[1])
                    );

                    // Check if HTTP method is tested
                    const methodCovered = analysis.methods.includes(route.method);

                    isCovered = pathCovered || methodCovered;
                }
            }

            if (isCovered) {
                coverage.covered++;
                coverage.fullCoverage.push(routeKey);
            } else {
                coverage.uncovered.push(routeKey);
                coverage.gapsByCategory[category].gaps.push(route);
            }
        });
    });

    // Generate report
    console.log('INFO: COVERAGE SUMMARY');
    console.log('='.repeat(50));
    console.log(`INFO: Total API Routes: ${coverage.total}`);
    console.log(`INFO: Covered Routes: ${coverage.covered}`);
    console.log(`INFO: Uncovered Routes: ${coverage.uncovered.length}`);
    console.log(`INFO: Coverage Percentage: ${((coverage.covered / coverage.total) * 100).toFixed(1)}%`);

    // Missing test files
    if (coverage.missingTestFiles.length > 0) {
        console.log('WARN: MISSING TEST FILES');
        console.log('='.repeat(50));
        coverage.missingTestFiles.forEach(missing => {
            console.log(`WARN: Missing ${missing.testFile} (${missing.routeCount} routes)`);
            missing.routes.forEach(route => {
                console.log(`WARN:   - ${route.method} ${route.path}`);
            });
        });
    }

    // Coverage gaps by category
    console.log('INFO: COVERAGE GAPS BY CATEGORY');
    console.log('='.repeat(50));
    Object.entries(coverage.gapsByCategory).forEach(([category, info]) => {
        if (info.gaps.length > 0) {
            console.log(`WARN: ${category.toUpperCase()} (${info.gaps.length} gaps):`);
            info.gaps.forEach(route => {
                console.log(`WARN:   ${route.method} ${route.path} - ${route.description}`);
            });
        }
    });

    // Test file analysis
    console.log('INFO: EXISTING TEST FILE ANALYSIS');
    console.log('='.repeat(50));
    Object.entries(testAnalysis).forEach(([file, analysis]) => {
        console.log(`INFO: ${file}:`);
        console.log(`INFO:   Lines: ${analysis.lineCount}`);
        console.log(`INFO:   Routes tested: ${analysis.routes.length}`);
        console.log(`INFO:   Methods: ${analysis.methods.join(', ') || 'None detected'}`);
        console.log(`INFO:   Auth tests: ${analysis.hasAuthTests ? 'Yes' : 'No'}`);
        console.log(`INFO:   Error tests: ${analysis.hasErrorTests ? 'Yes' : 'No'}`);
        console.log(`INFO:   Validation tests: ${analysis.hasValidationTests ? 'Yes' : 'No'}`);
    });

    // Recommendations
    console.log('INFO: RECOMMENDATIONS');
    console.log('='.repeat(50));

    const recommendations = [];

    if (coverage.missingTestFiles.length > 0) {
        recommendations.push(`Create ${coverage.missingTestFiles.length} missing test files`);
    }

    const lowCoverageCategories = Object.entries(coverage.gapsByCategory)
        .filter(([_, info]) => info.gaps.length > info.routes.length * 0.5)
        .map(([category]) => category);

    if (lowCoverageCategories.length > 0) {
        recommendations.push(`Improve coverage for: ${lowCoverageCategories.join(', ')}`);
    }

    const filesNeedingAuth = Object.entries(testAnalysis)
        .filter(([_, analysis]) => !analysis.hasAuthTests)
        .map(([file]) => file);

    if (filesNeedingAuth.length > 0) {
        recommendations.push(`Add authentication tests to: ${filesNeedingAuth.slice(0, 3).join(', ')}${filesNeedingAuth.length > 3 ? '...' : ''}`);
    }

    recommendations.forEach((rec, index) => {
        console.log(`INFO: ${index + 1}. ${rec}`);
    });

    // Save detailed report
    const reportPath = path.join(__dirname, '../TEST_COVERAGE_ANALYSIS.json');
    fs.writeFileSync(reportPath, JSON.stringify({
        timestamp: new Date().toISOString(),
        summary: {
            totalRoutes: coverage.total,
            coveredRoutes: coverage.covered,
            uncoveredRoutes: coverage.uncovered.length,
            coveragePercentage: ((coverage.covered / coverage.total) * 100).toFixed(1)
        },
        coverage,
        testAnalysis,
        recommendations
    }, null, 2));

    console.log(`INFO: Detailed report saved to: ${reportPath}`);

    return coverage;
}

// Run the analysis
if (require.main === module) {
    generateCoverageReport();
}

module.exports = { generateCoverageReport };