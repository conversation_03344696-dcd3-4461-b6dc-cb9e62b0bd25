#!/usr/bin/env node

/**
 * Clean Remaining Test Logs Script
 * 
 * This script removes remaining console.log statements from test files that don't have proper INFO/WARN/ERROR tags.
 */

const fs = require('fs');
const path = require('path');

const testsDir = path.join(__dirname, '../tests');

function cleanTestFile(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        let modified = false;

        // Remove all remaining console.log statements with emoji or status messages
        const patterns = [
            // Remove all emoji-based console.log statements
            /\s*console\.log\('✅[^']*'\);\s*\n/g,
            /\s*console\.log\('⚠️[^']*'\);\s*\n/g,
            /\s*console\.log\('❌[^']*'\);\s*\n/g,
            /\s*console\.log\('🎉[^']*'\);\s*\n/g,

            // Remove template literal console.log with emoji
            /\s*console\.log\(`✅[^`]*`\);\s*\n/g,
            /\s*console\.log\(`⚠️[^`]*`\);\s*\n/g,
            /\s*console\.log\(`❌[^`]*`\);\s*\n/g,

            // Remove status messages with colons and error codes
            /\s*console\.log\('[^']*handled:'[^)]+\);\s*\n/g,
            /\s*console\.log\('[^']*step:'[^)]+\);\s*\n/g,
            /\s*console\.log\('[^']*failed:'[^)]+\);\s*\n/g,

            // Remove cleanup messages
            /\s*console\.log\('Test user cleanup[^']*'\);\s*\n/g,
            /\s*console\.log\('Cleanup error[^']*'\);\s*\n/g,
        ];

        patterns.forEach(pattern => {
            const newContent = content.replace(pattern, '');
            if (newContent !== content) {
                content = newContent;
                modified = true;
            }
        });

        if (modified) {
            fs.writeFileSync(filePath, content);
            console.log(`INFO: Cleaned remaining logs in ${path.basename(filePath)}`);
            return true;
        }

        return false;
    } catch (error) {
        console.error(`ERROR: Failed to clean ${filePath}:`, error.message);
        return false;
    }
}

function cleanAllTestFiles() {
    console.log('INFO: Starting remaining test log cleanup...');

    try {
        const files = fs.readdirSync(testsDir);
        const testFiles = files.filter(file => file.endsWith('.test.ts'));

        let cleanedCount = 0;

        testFiles.forEach(file => {
            const filePath = path.join(testsDir, file);
            if (cleanTestFile(filePath)) {
                cleanedCount++;
            }
        });

        console.log(`INFO: Remaining cleanup complete. Modified ${cleanedCount} files.`);

    } catch (error) {
        console.error('ERROR: Failed to clean test files:', error.message);
        process.exit(1);
    }
}

// Run cleanup
if (require.main === module) {
    cleanAllTestFiles();
}

module.exports = { cleanAllTestFiles };