#!/usr/bin/env ts-node
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const client_cognito_identity_provider_1 = require("@aws-sdk/client-cognito-identity-provider");
const client_dynamodb_1 = require("@aws-sdk/client-dynamodb");
const lib_dynamodb_1 = require("@aws-sdk/lib-dynamodb");
// Configuration
const ENVIRONMENT = process.env.ENVIRONMENT || 'staging';
// Region mapping based on environment
const getRegionForEnvironment = (env) => {
    switch (env) {
        case 'production':
            return 'us-east-1';
        case 'staging':
        case 'development':
            return 'us-west-2';
        default:
            return 'us-west-2'; // Default to staging region
    }
};
const AWS_REGION = process.env.AWS_REGION || getRegionForEnvironment(ENVIRONMENT);
// Initialize AWS clients
const cognitoClient = new client_cognito_identity_provider_1.CognitoIdentityProviderClient({ region: AWS_REGION });
const dynamoClient = new client_dynamodb_1.DynamoDBClient({ region: AWS_REGION });
const dynamodb = lib_dynamodb_1.DynamoDBDocumentClient.from(dynamoClient);
// Table names
const USERS_TABLE = `gameflex-users-${ENVIRONMENT}`;
async function findUserPoolId() {
    try {
        const { UserPools } = await cognitoClient.send(new (await import('@aws-sdk/client-cognito-identity-provider')).ListUserPoolsCommand({
            MaxResults: 50
        }));
        if (!UserPools)
            return null;
        // Look for user pool with the environment name
        const targetPool = UserPools.find(pool => pool.Name?.includes(`gameflex-users-${ENVIRONMENT}`) ||
            pool.Name?.includes(`gameflex-user-pool-${ENVIRONMENT}`));
        if (targetPool) {
            return targetPool.Id || null;
        }
        // If no environment-specific pool found, list all pools for user to choose
        console.log('Available User Pools:');
        UserPools.forEach((pool, index) => {
            console.log(`${index + 1}. ${pool.Name} (${pool.Id})`);
        });
        return null;
    }
    catch (error) {
        console.error('Error finding user pool:', error);
        return null;
    }
}
async function deleteTestUser(email) {
    console.log(`🗑️  Deleting test user: ${email}`);
    console.log(`Environment: ${ENVIRONMENT}`);
    console.log(`Region: ${AWS_REGION}`);
    console.log('');
    try {
        // Step 1: Find the correct User Pool ID
        const userPoolId = await findUserPoolId();
        if (!userPoolId) {
            console.error('❌ Could not find User Pool for environment:', ENVIRONMENT);
            console.log('Please check the available pools above and update the ENVIRONMENT variable.');
            process.exit(1);
        }
        console.log(`📋 Using User Pool: ${userPoolId}`);
        console.log('');
        // Step 2: Find and delete user from Cognito
        await deleteCognitoUser(email, userPoolId);
        // Step 3: Find and delete user from DynamoDB
        await deleteDynamoDBUser(email);
        console.log('✅ Test user deletion completed successfully!');
    }
    catch (error) {
        console.error('❌ Error during user deletion:', error);
        process.exit(1);
    }
}
async function deleteCognitoUser(email, userPoolId) {
    console.log('🔍 Searching for user in Cognito...');
    try {
        // First, try to find the user by email
        const listCommand = new client_cognito_identity_provider_1.ListUsersCommand({
            UserPoolId: userPoolId,
            Filter: `email = "${email.toLowerCase()}"`,
            Limit: 1
        });
        const listResult = await cognitoClient.send(listCommand);
        if (!listResult.Users || listResult.Users.length === 0) {
            console.log('⚠️  User not found in Cognito');
            return;
        }
        const user = listResult.Users[0];
        const username = user.Username;
        if (!username) {
            console.log('⚠️  Username not found for user');
            return;
        }
        console.log(`📧 Found Cognito user: ${username}`);
        // Delete the user
        const deleteCommand = new client_cognito_identity_provider_1.AdminDeleteUserCommand({
            UserPoolId: userPoolId,
            Username: username
        });
        await cognitoClient.send(deleteCommand);
        console.log('✅ User deleted from Cognito');
    }
    catch (error) {
        if (error.name === 'UserNotFoundException') {
            console.log('⚠️  User not found in Cognito');
        }
        else {
            console.error('❌ Error deleting from Cognito:', error.message);
            throw error;
        }
    }
}
async function deleteDynamoDBUser(email) {
    console.log('🔍 Searching for user in DynamoDB...');
    try {
        // Scan for user by email (since email is not the primary key)
        const scanCommand = new lib_dynamodb_1.ScanCommand({
            TableName: USERS_TABLE,
            FilterExpression: 'email = :email',
            ExpressionAttributeValues: {
                ':email': email.toLowerCase()
            }
        });
        const scanResult = await dynamodb.send(scanCommand);
        if (!scanResult.Items || scanResult.Items.length === 0) {
            console.log('⚠️  User not found in DynamoDB');
            return;
        }
        // Delete all matching users (there should only be one)
        for (const item of scanResult.Items) {
            const userRecord = item;
            console.log(`📧 Found DynamoDB user: ${userRecord.id} (${userRecord.email})`);
            const deleteCommand = new lib_dynamodb_1.DeleteCommand({
                TableName: USERS_TABLE,
                Key: {
                    id: userRecord.id
                }
            });
            await dynamodb.send(deleteCommand);
            console.log(`✅ User ${userRecord.id} deleted from DynamoDB`);
        }
    }
    catch (error) {
        if (error.name === 'ResourceNotFoundException') {
            console.log(`⚠️  DynamoDB table '${USERS_TABLE}' not found - skipping DynamoDB cleanup`);
        }
        else {
            console.error('❌ Error deleting from DynamoDB:', error.message);
            throw error;
        }
    }
}
// Main execution
async function main() {
    const email = process.argv[2];
    if (!email) {
        console.error('❌ Please provide an email address');
        console.log('Usage: npm run delete-test-user <email>');
        console.log('Example: npm run delete-test-user <EMAIL>');
        process.exit(1);
    }
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        console.error('❌ Invalid email format');
        process.exit(1);
    }
    await deleteTestUser(email);
}
// Run the script
if (require.main === module) {
    main().catch(console.error);
}
//# sourceMappingURL=data:application/json;base64,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