#!/usr/bin/env node
"use strict";
/**
 * Deployment script that handles resource imports for existing AWS resources
 * This script checks for existing resources and imports them if they exist,
 * or creates new ones if they don't.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeploymentManager = void 0;
const child_process_1 = require("child_process");
const client_dynamodb_1 = require("@aws-sdk/client-dynamodb");
const client_s3_1 = require("@aws-sdk/client-s3");
const client_cognito_identity_provider_1 = require("@aws-sdk/client-cognito-identity-provider");
class DeploymentManager {
    environment;
    projectName;
    region;
    stackName;
    constructor() {
        this.environment = process.env.ENVIRONMENT || 'staging';
        this.projectName = process.env.PROJECT_NAME || 'gameflex';
        this.region = process.env.AWS_REGION || 'us-west-2';
        this.stackName = `${this.projectName}-${this.environment}`;
    }
    async checkExistingResources() {
        const mappings = [];
        console.log('🔍 Checking for existing AWS resources...');
        // Check DynamoDB tables
        const dynamoMappings = await this.checkDynamoDBTables();
        mappings.push(...dynamoMappings);
        // Check S3 buckets
        const s3Mappings = await this.checkS3Buckets();
        mappings.push(...s3Mappings);
        // Check Cognito User Pools
        const cognitoMappings = await this.checkCognitoUserPools();
        mappings.push(...cognitoMappings);
        return mappings;
    }
    async checkDynamoDBTables() {
        const client = new client_dynamodb_1.DynamoDBClient({ region: this.region });
        const mappings = [];
        const tableNames = [
            'Posts', 'Media', 'UserProfiles', 'Comments', 'Likes', 'Follows',
            'Channels', 'ChannelMembers', 'Reflexes', 'ReflexLikes', 'PostReactions',
            'ReflexReactions', 'Users', 'XboxAccounts'
        ];
        for (const tableName of tableNames) {
            const fullTableName = `${this.projectName}-${this.environment}-${tableName}`;
            try {
                const command = new client_dynamodb_1.DescribeTableCommand({ TableName: fullTableName });
                const response = await client.send(command);
                if (response.Table) {
                    mappings.push({
                        logicalId: `${tableName}Table`,
                        physicalResourceId: fullTableName,
                        resourceType: 'AWS::DynamoDB::Table'
                    });
                    console.log(`✅ Found existing DynamoDB table: ${fullTableName}`);
                }
            }
            catch (error) {
                console.log(`❌ DynamoDB table not found: ${fullTableName}`);
            }
        }
        return mappings;
    }
    async checkS3Buckets() {
        const client = new client_s3_1.S3Client({ region: this.region });
        const mappings = [];
        const bucketName = `${this.projectName}-media-${this.environment}`;
        try {
            const command = new client_s3_1.HeadBucketCommand({ Bucket: bucketName });
            await client.send(command);
            mappings.push({
                logicalId: 'MediaBucket',
                physicalResourceId: bucketName,
                resourceType: 'AWS::S3::Bucket'
            });
            console.log(`✅ Found existing S3 bucket: ${bucketName}`);
        }
        catch (error) {
            console.log(`❌ S3 bucket not found: ${bucketName}`);
        }
        return mappings;
    }
    async checkCognitoUserPools() {
        const client = new client_cognito_identity_provider_1.CognitoIdentityProviderClient({ region: this.region });
        const mappings = [];
        const userPoolName = `${this.projectName}-users-${this.environment}`;
        try {
            const command = new client_cognito_identity_provider_1.ListUserPoolsCommand({ MaxResults: 60 });
            const response = await client.send(command);
            const userPool = response.UserPools?.find(pool => pool.Name === userPoolName);
            if (userPool && userPool.Id) {
                mappings.push({
                    logicalId: 'UserPool',
                    physicalResourceId: userPool.Id,
                    resourceType: 'AWS::Cognito::UserPool'
                });
                console.log(`✅ Found existing Cognito User Pool: ${userPoolName} (${userPool.Id})`);
            }
        }
        catch (error) {
            console.log(`❌ Cognito User Pool not found: ${userPoolName}`);
        }
        return mappings;
    }
    async deployWithImport() {
        console.log(`🚀 Starting deployment for ${this.stackName}...`);
        // Check for existing resources
        const existingResources = await this.checkExistingResources();
        if (existingResources.length > 0) {
            console.log(`\n📦 Found ${existingResources.length} existing resources to import:`);
            existingResources.forEach(resource => {
                console.log(`  - ${resource.logicalId}: ${resource.physicalResourceId}`);
            });
            // Create resource mapping file for CDK import
            const mappingFile = this.createResourceMappingFile(existingResources);
            console.log('\n🔄 Performing CDK import...');
            try {
                (0, child_process_1.execSync)(`cdk import ${this.stackName} --resource-mapping ${mappingFile}`, {
                    stdio: 'inherit',
                    cwd: process.cwd()
                });
                console.log('✅ Import completed successfully');
            }
            catch (error) {
                console.log('⚠️  Import failed, proceeding with regular deployment...');
            }
        }
        // Deploy the stack
        console.log('\n🏗️  Deploying stack...');
        (0, child_process_1.execSync)(`cdk deploy ${this.stackName} --require-approval never`, {
            stdio: 'inherit',
            cwd: process.cwd()
        });
        console.log('✅ Deployment completed successfully!');
    }
    createResourceMappingFile(resources) {
        const mappingContent = resources.map(resource => `${resource.logicalId}=${resource.physicalResourceId}`).join('\n');
        const filename = `/tmp/resource-mapping-${Date.now()}.txt`;
        require('fs').writeFileSync(filename, mappingContent);
        return filename;
    }
}
exports.DeploymentManager = DeploymentManager;
// Main execution
async function main() {
    const manager = new DeploymentManager();
    try {
        await manager.deployWithImport();
    }
    catch (error) {
        console.error('❌ Deployment failed:', error);
        process.exit(1);
    }
}
if (require.main === module) {
    main();
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZGVwbG95LXdpdGgtaW1wb3J0LmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiZGVwbG95LXdpdGgtaW1wb3J0LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7O0FBRUE7Ozs7R0FJRzs7O0FBRUgsaURBQXlDO0FBQ3pDLDhEQUFtRztBQUNuRyxrREFBcUY7QUFDckYsZ0dBQWdIO0FBUWhILE1BQU0saUJBQWlCO0lBQ2IsV0FBVyxDQUFTO0lBQ3BCLFdBQVcsQ0FBUztJQUNwQixNQUFNLENBQVM7SUFDZixTQUFTLENBQVM7SUFFMUI7UUFDRSxJQUFJLENBQUMsV0FBVyxHQUFHLE9BQU8sQ0FBQyxHQUFHLENBQUMsV0FBVyxJQUFJLFNBQVMsQ0FBQztRQUN4RCxJQUFJLENBQUMsV0FBVyxHQUFHLE9BQU8sQ0FBQyxHQUFHLENBQUMsWUFBWSxJQUFJLFVBQVUsQ0FBQztRQUMxRCxJQUFJLENBQUMsTUFBTSxHQUFHLE9BQU8sQ0FBQyxHQUFHLENBQUMsVUFBVSxJQUFJLFdBQVcsQ0FBQztRQUNwRCxJQUFJLENBQUMsU0FBUyxHQUFHLEdBQUcsSUFBSSxDQUFDLFdBQVcsSUFBSSxJQUFJLENBQUMsV0FBVyxFQUFFLENBQUM7SUFDN0QsQ0FBQztJQUVELEtBQUssQ0FBQyxzQkFBc0I7UUFDMUIsTUFBTSxRQUFRLEdBQXNCLEVBQUUsQ0FBQztRQUV2QyxPQUFPLENBQUMsR0FBRyxDQUFDLDJDQUEyQyxDQUFDLENBQUM7UUFFekQsd0JBQXdCO1FBQ3hCLE1BQU0sY0FBYyxHQUFHLE1BQU0sSUFBSSxDQUFDLG1CQUFtQixFQUFFLENBQUM7UUFDeEQsUUFBUSxDQUFDLElBQUksQ0FBQyxHQUFHLGNBQWMsQ0FBQyxDQUFDO1FBRWpDLG1CQUFtQjtRQUNuQixNQUFNLFVBQVUsR0FBRyxNQUFNLElBQUksQ0FBQyxjQUFjLEVBQUUsQ0FBQztRQUMvQyxRQUFRLENBQUMsSUFBSSxDQUFDLEdBQUcsVUFBVSxDQUFDLENBQUM7UUFFN0IsMkJBQTJCO1FBQzNCLE1BQU0sZUFBZSxHQUFHLE1BQU0sSUFBSSxDQUFDLHFCQUFxQixFQUFFLENBQUM7UUFDM0QsUUFBUSxDQUFDLElBQUksQ0FBQyxHQUFHLGVBQWUsQ0FBQyxDQUFDO1FBRWxDLE9BQU8sUUFBUSxDQUFDO0lBQ2xCLENBQUM7SUFFTyxLQUFLLENBQUMsbUJBQW1CO1FBQy9CLE1BQU0sTUFBTSxHQUFHLElBQUksZ0NBQWMsQ0FBQyxFQUFFLE1BQU0sRUFBRSxJQUFJLENBQUMsTUFBTSxFQUFFLENBQUMsQ0FBQztRQUMzRCxNQUFNLFFBQVEsR0FBc0IsRUFBRSxDQUFDO1FBRXZDLE1BQU0sVUFBVSxHQUFHO1lBQ2pCLE9BQU8sRUFBRSxPQUFPLEVBQUUsY0FBYyxFQUFFLFVBQVUsRUFBRSxPQUFPLEVBQUUsU0FBUztZQUNoRSxVQUFVLEVBQUUsZ0JBQWdCLEVBQUUsVUFBVSxFQUFFLGFBQWEsRUFBRSxlQUFlO1lBQ3hFLGlCQUFpQixFQUFFLE9BQU8sRUFBRSxjQUFjO1NBQzNDLENBQUM7UUFFRixLQUFLLE1BQU0sU0FBUyxJQUFJLFVBQVUsRUFBRSxDQUFDO1lBQ25DLE1BQU0sYUFBYSxHQUFHLEdBQUcsSUFBSSxDQUFDLFdBQVcsSUFBSSxJQUFJLENBQUMsV0FBVyxJQUFJLFNBQVMsRUFBRSxDQUFDO1lBRTdFLElBQUksQ0FBQztnQkFDSCxNQUFNLE9BQU8sR0FBRyxJQUFJLHNDQUFvQixDQUFDLEVBQUUsU0FBUyxFQUFFLGFBQWEsRUFBRSxDQUFDLENBQUM7Z0JBQ3ZFLE1BQU0sUUFBUSxHQUFHLE1BQU0sTUFBTSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FBQztnQkFFNUMsSUFBSSxRQUFRLENBQUMsS0FBSyxFQUFFLENBQUM7b0JBQ25CLFFBQVEsQ0FBQyxJQUFJLENBQUM7d0JBQ1osU0FBUyxFQUFFLEdBQUcsU0FBUyxPQUFPO3dCQUM5QixrQkFBa0IsRUFBRSxhQUFhO3dCQUNqQyxZQUFZLEVBQUUsc0JBQXNCO3FCQUNyQyxDQUFDLENBQUM7b0JBQ0gsT0FBTyxDQUFDLEdBQUcsQ0FBQyxvQ0FBb0MsYUFBYSxFQUFFLENBQUMsQ0FBQztnQkFDbkUsQ0FBQztZQUNILENBQUM7WUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO2dCQUNmLE9BQU8sQ0FBQyxHQUFHLENBQUMsK0JBQStCLGFBQWEsRUFBRSxDQUFDLENBQUM7WUFDOUQsQ0FBQztRQUNILENBQUM7UUFFRCxPQUFPLFFBQVEsQ0FBQztJQUNsQixDQUFDO0lBRU8sS0FBSyxDQUFDLGNBQWM7UUFDMUIsTUFBTSxNQUFNLEdBQUcsSUFBSSxvQkFBUSxDQUFDLEVBQUUsTUFBTSxFQUFFLElBQUksQ0FBQyxNQUFNLEVBQUUsQ0FBQyxDQUFDO1FBQ3JELE1BQU0sUUFBUSxHQUFzQixFQUFFLENBQUM7UUFFdkMsTUFBTSxVQUFVLEdBQUcsR0FBRyxJQUFJLENBQUMsV0FBVyxVQUFVLElBQUksQ0FBQyxXQUFXLEVBQUUsQ0FBQztRQUVuRSxJQUFJLENBQUM7WUFDSCxNQUFNLE9BQU8sR0FBRyxJQUFJLDZCQUFpQixDQUFDLEVBQUUsTUFBTSxFQUFFLFVBQVUsRUFBRSxDQUFDLENBQUM7WUFDOUQsTUFBTSxNQUFNLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxDQUFDO1lBRTNCLFFBQVEsQ0FBQyxJQUFJLENBQUM7Z0JBQ1osU0FBUyxFQUFFLGFBQWE7Z0JBQ3hCLGtCQUFrQixFQUFFLFVBQVU7Z0JBQzlCLFlBQVksRUFBRSxpQkFBaUI7YUFDaEMsQ0FBQyxDQUFDO1lBQ0gsT0FBTyxDQUFDLEdBQUcsQ0FBQywrQkFBK0IsVUFBVSxFQUFFLENBQUMsQ0FBQztRQUMzRCxDQUFDO1FBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztZQUNmLE9BQU8sQ0FBQyxHQUFHLENBQUMsMEJBQTBCLFVBQVUsRUFBRSxDQUFDLENBQUM7UUFDdEQsQ0FBQztRQUVELE9BQU8sUUFBUSxDQUFDO0lBQ2xCLENBQUM7SUFFTyxLQUFLLENBQUMscUJBQXFCO1FBQ2pDLE1BQU0sTUFBTSxHQUFHLElBQUksZ0VBQTZCLENBQUMsRUFBRSxNQUFNLEVBQUUsSUFBSSxDQUFDLE1BQU0sRUFBRSxDQUFDLENBQUM7UUFDMUUsTUFBTSxRQUFRLEdBQXNCLEVBQUUsQ0FBQztRQUV2QyxNQUFNLFlBQVksR0FBRyxHQUFHLElBQUksQ0FBQyxXQUFXLFVBQVUsSUFBSSxDQUFDLFdBQVcsRUFBRSxDQUFDO1FBRXJFLElBQUksQ0FBQztZQUNILE1BQU0sT0FBTyxHQUFHLElBQUksdURBQW9CLENBQUMsRUFBRSxVQUFVLEVBQUUsRUFBRSxFQUFFLENBQUMsQ0FBQztZQUM3RCxNQUFNLFFBQVEsR0FBRyxNQUFNLE1BQU0sQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLENBQUM7WUFFNUMsTUFBTSxRQUFRLEdBQUcsUUFBUSxDQUFDLFNBQVMsRUFBRSxJQUFJLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUMsSUFBSSxLQUFLLFlBQVksQ0FBQyxDQUFDO1lBRTlFLElBQUksUUFBUSxJQUFJLFFBQVEsQ0FBQyxFQUFFLEVBQUUsQ0FBQztnQkFDNUIsUUFBUSxDQUFDLElBQUksQ0FBQztvQkFDWixTQUFTLEVBQUUsVUFBVTtvQkFDckIsa0JBQWtCLEVBQUUsUUFBUSxDQUFDLEVBQUU7b0JBQy9CLFlBQVksRUFBRSx3QkFBd0I7aUJBQ3ZDLENBQUMsQ0FBQztnQkFDSCxPQUFPLENBQUMsR0FBRyxDQUFDLHVDQUF1QyxZQUFZLEtBQUssUUFBUSxDQUFDLEVBQUUsR0FBRyxDQUFDLENBQUM7WUFDdEYsQ0FBQztRQUNILENBQUM7UUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1lBQ2YsT0FBTyxDQUFDLEdBQUcsQ0FBQyxrQ0FBa0MsWUFBWSxFQUFFLENBQUMsQ0FBQztRQUNoRSxDQUFDO1FBRUQsT0FBTyxRQUFRLENBQUM7SUFDbEIsQ0FBQztJQUVELEtBQUssQ0FBQyxnQkFBZ0I7UUFDcEIsT0FBTyxDQUFDLEdBQUcsQ0FBQyw4QkFBOEIsSUFBSSxDQUFDLFNBQVMsS0FBSyxDQUFDLENBQUM7UUFFL0QsK0JBQStCO1FBQy9CLE1BQU0saUJBQWlCLEdBQUcsTUFBTSxJQUFJLENBQUMsc0JBQXNCLEVBQUUsQ0FBQztRQUU5RCxJQUFJLGlCQUFpQixDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUUsQ0FBQztZQUNqQyxPQUFPLENBQUMsR0FBRyxDQUFDLGNBQWMsaUJBQWlCLENBQUMsTUFBTSxnQ0FBZ0MsQ0FBQyxDQUFDO1lBQ3BGLGlCQUFpQixDQUFDLE9BQU8sQ0FBQyxRQUFRLENBQUMsRUFBRTtnQkFDbkMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxPQUFPLFFBQVEsQ0FBQyxTQUFTLEtBQUssUUFBUSxDQUFDLGtCQUFrQixFQUFFLENBQUMsQ0FBQztZQUMzRSxDQUFDLENBQUMsQ0FBQztZQUVILDhDQUE4QztZQUM5QyxNQUFNLFdBQVcsR0FBRyxJQUFJLENBQUMseUJBQXlCLENBQUMsaUJBQWlCLENBQUMsQ0FBQztZQUV0RSxPQUFPLENBQUMsR0FBRyxDQUFDLCtCQUErQixDQUFDLENBQUM7WUFDN0MsSUFBSSxDQUFDO2dCQUNILElBQUEsd0JBQVEsRUFBQyxjQUFjLElBQUksQ0FBQyxTQUFTLHVCQUF1QixXQUFXLEVBQUUsRUFBRTtvQkFDekUsS0FBSyxFQUFFLFNBQVM7b0JBQ2hCLEdBQUcsRUFBRSxPQUFPLENBQUMsR0FBRyxFQUFFO2lCQUNuQixDQUFDLENBQUM7Z0JBQ0gsT0FBTyxDQUFDLEdBQUcsQ0FBQyxpQ0FBaUMsQ0FBQyxDQUFDO1lBQ2pELENBQUM7WUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO2dCQUNmLE9BQU8sQ0FBQyxHQUFHLENBQUMsMERBQTBELENBQUMsQ0FBQztZQUMxRSxDQUFDO1FBQ0gsQ0FBQztRQUVELG1CQUFtQjtRQUNuQixPQUFPLENBQUMsR0FBRyxDQUFDLDJCQUEyQixDQUFDLENBQUM7UUFDekMsSUFBQSx3QkFBUSxFQUFDLGNBQWMsSUFBSSxDQUFDLFNBQVMsMkJBQTJCLEVBQUU7WUFDaEUsS0FBSyxFQUFFLFNBQVM7WUFDaEIsR0FBRyxFQUFFLE9BQU8sQ0FBQyxHQUFHLEVBQUU7U0FDbkIsQ0FBQyxDQUFDO1FBRUgsT0FBTyxDQUFDLEdBQUcsQ0FBQyxzQ0FBc0MsQ0FBQyxDQUFDO0lBQ3RELENBQUM7SUFFTyx5QkFBeUIsQ0FBQyxTQUE0QjtRQUM1RCxNQUFNLGNBQWMsR0FBRyxTQUFTLENBQUMsR0FBRyxDQUFDLFFBQVEsQ0FBQyxFQUFFLENBQzlDLEdBQUcsUUFBUSxDQUFDLFNBQVMsSUFBSSxRQUFRLENBQUMsa0JBQWtCLEVBQUUsQ0FDdkQsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7UUFFYixNQUFNLFFBQVEsR0FBRyx5QkFBeUIsSUFBSSxDQUFDLEdBQUcsRUFBRSxNQUFNLENBQUM7UUFDM0QsT0FBTyxDQUFDLElBQUksQ0FBQyxDQUFDLGFBQWEsQ0FBQyxRQUFRLEVBQUUsY0FBYyxDQUFDLENBQUM7UUFFdEQsT0FBTyxRQUFRLENBQUM7SUFDbEIsQ0FBQztDQUNGO0FBa0JRLDhDQUFpQjtBQWhCMUIsaUJBQWlCO0FBQ2pCLEtBQUssVUFBVSxJQUFJO0lBQ2pCLE1BQU0sT0FBTyxHQUFHLElBQUksaUJBQWlCLEVBQUUsQ0FBQztJQUV4QyxJQUFJLENBQUM7UUFDSCxNQUFNLE9BQU8sQ0FBQyxnQkFBZ0IsRUFBRSxDQUFDO0lBQ25DLENBQUM7SUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1FBQ2YsT0FBTyxDQUFDLEtBQUssQ0FBQyxzQkFBc0IsRUFBRSxLQUFLLENBQUMsQ0FBQztRQUM3QyxPQUFPLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDO0lBQ2xCLENBQUM7QUFDSCxDQUFDO0FBRUQsSUFBSSxPQUFPLENBQUMsSUFBSSxLQUFLLE1BQU0sRUFBRSxDQUFDO0lBQzVCLElBQUksRUFBRSxDQUFDO0FBQ1QsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIiMhL3Vzci9iaW4vZW52IG5vZGVcblxuLyoqXG4gKiBEZXBsb3ltZW50IHNjcmlwdCB0aGF0IGhhbmRsZXMgcmVzb3VyY2UgaW1wb3J0cyBmb3IgZXhpc3RpbmcgQVdTIHJlc291cmNlc1xuICogVGhpcyBzY3JpcHQgY2hlY2tzIGZvciBleGlzdGluZyByZXNvdXJjZXMgYW5kIGltcG9ydHMgdGhlbSBpZiB0aGV5IGV4aXN0LFxuICogb3IgY3JlYXRlcyBuZXcgb25lcyBpZiB0aGV5IGRvbid0LlxuICovXG5cbmltcG9ydCB7IGV4ZWNTeW5jIH0gZnJvbSAnY2hpbGRfcHJvY2Vzcyc7XG5pbXBvcnQgeyBEeW5hbW9EQkNsaWVudCwgTGlzdFRhYmxlc0NvbW1hbmQsIERlc2NyaWJlVGFibGVDb21tYW5kIH0gZnJvbSAnQGF3cy1zZGsvY2xpZW50LWR5bmFtb2RiJztcbmltcG9ydCB7IFMzQ2xpZW50LCBMaXN0QnVja2V0c0NvbW1hbmQsIEhlYWRCdWNrZXRDb21tYW5kIH0gZnJvbSAnQGF3cy1zZGsvY2xpZW50LXMzJztcbmltcG9ydCB7IENvZ25pdG9JZGVudGl0eVByb3ZpZGVyQ2xpZW50LCBMaXN0VXNlclBvb2xzQ29tbWFuZCB9IGZyb20gJ0Bhd3Mtc2RrL2NsaWVudC1jb2duaXRvLWlkZW50aXR5LXByb3ZpZGVyJztcblxuaW50ZXJmYWNlIFJlc291cmNlTWFwcGluZyB7XG4gIGxvZ2ljYWxJZDogc3RyaW5nO1xuICBwaHlzaWNhbFJlc291cmNlSWQ6IHN0cmluZztcbiAgcmVzb3VyY2VUeXBlOiBzdHJpbmc7XG59XG5cbmNsYXNzIERlcGxveW1lbnRNYW5hZ2VyIHtcbiAgcHJpdmF0ZSBlbnZpcm9ubWVudDogc3RyaW5nO1xuICBwcml2YXRlIHByb2plY3ROYW1lOiBzdHJpbmc7XG4gIHByaXZhdGUgcmVnaW9uOiBzdHJpbmc7XG4gIHByaXZhdGUgc3RhY2tOYW1lOiBzdHJpbmc7XG5cbiAgY29uc3RydWN0b3IoKSB7XG4gICAgdGhpcy5lbnZpcm9ubWVudCA9IHByb2Nlc3MuZW52LkVOVklST05NRU5UIHx8ICdzdGFnaW5nJztcbiAgICB0aGlzLnByb2plY3ROYW1lID0gcHJvY2Vzcy5lbnYuUFJPSkVDVF9OQU1FIHx8ICdnYW1lZmxleCc7XG4gICAgdGhpcy5yZWdpb24gPSBwcm9jZXNzLmVudi5BV1NfUkVHSU9OIHx8ICd1cy13ZXN0LTInO1xuICAgIHRoaXMuc3RhY2tOYW1lID0gYCR7dGhpcy5wcm9qZWN0TmFtZX0tJHt0aGlzLmVudmlyb25tZW50fWA7XG4gIH1cblxuICBhc3luYyBjaGVja0V4aXN0aW5nUmVzb3VyY2VzKCk6IFByb21pc2U8UmVzb3VyY2VNYXBwaW5nW10+IHtcbiAgICBjb25zdCBtYXBwaW5nczogUmVzb3VyY2VNYXBwaW5nW10gPSBbXTtcblxuICAgIGNvbnNvbGUubG9nKCfwn5SNIENoZWNraW5nIGZvciBleGlzdGluZyBBV1MgcmVzb3VyY2VzLi4uJyk7XG5cbiAgICAvLyBDaGVjayBEeW5hbW9EQiB0YWJsZXNcbiAgICBjb25zdCBkeW5hbW9NYXBwaW5ncyA9IGF3YWl0IHRoaXMuY2hlY2tEeW5hbW9EQlRhYmxlcygpO1xuICAgIG1hcHBpbmdzLnB1c2goLi4uZHluYW1vTWFwcGluZ3MpO1xuXG4gICAgLy8gQ2hlY2sgUzMgYnVja2V0c1xuICAgIGNvbnN0IHMzTWFwcGluZ3MgPSBhd2FpdCB0aGlzLmNoZWNrUzNCdWNrZXRzKCk7XG4gICAgbWFwcGluZ3MucHVzaCguLi5zM01hcHBpbmdzKTtcblxuICAgIC8vIENoZWNrIENvZ25pdG8gVXNlciBQb29sc1xuICAgIGNvbnN0IGNvZ25pdG9NYXBwaW5ncyA9IGF3YWl0IHRoaXMuY2hlY2tDb2duaXRvVXNlclBvb2xzKCk7XG4gICAgbWFwcGluZ3MucHVzaCguLi5jb2duaXRvTWFwcGluZ3MpO1xuXG4gICAgcmV0dXJuIG1hcHBpbmdzO1xuICB9XG5cbiAgcHJpdmF0ZSBhc3luYyBjaGVja0R5bmFtb0RCVGFibGVzKCk6IFByb21pc2U8UmVzb3VyY2VNYXBwaW5nW10+IHtcbiAgICBjb25zdCBjbGllbnQgPSBuZXcgRHluYW1vREJDbGllbnQoeyByZWdpb246IHRoaXMucmVnaW9uIH0pO1xuICAgIGNvbnN0IG1hcHBpbmdzOiBSZXNvdXJjZU1hcHBpbmdbXSA9IFtdO1xuXG4gICAgY29uc3QgdGFibGVOYW1lcyA9IFtcbiAgICAgICdQb3N0cycsICdNZWRpYScsICdVc2VyUHJvZmlsZXMnLCAnQ29tbWVudHMnLCAnTGlrZXMnLCAnRm9sbG93cycsXG4gICAgICAnQ2hhbm5lbHMnLCAnQ2hhbm5lbE1lbWJlcnMnLCAnUmVmbGV4ZXMnLCAnUmVmbGV4TGlrZXMnLCAnUG9zdFJlYWN0aW9ucycsXG4gICAgICAnUmVmbGV4UmVhY3Rpb25zJywgJ1VzZXJzJywgJ1hib3hBY2NvdW50cydcbiAgICBdO1xuXG4gICAgZm9yIChjb25zdCB0YWJsZU5hbWUgb2YgdGFibGVOYW1lcykge1xuICAgICAgY29uc3QgZnVsbFRhYmxlTmFtZSA9IGAke3RoaXMucHJvamVjdE5hbWV9LSR7dGhpcy5lbnZpcm9ubWVudH0tJHt0YWJsZU5hbWV9YDtcbiAgICAgIFxuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgY29tbWFuZCA9IG5ldyBEZXNjcmliZVRhYmxlQ29tbWFuZCh7IFRhYmxlTmFtZTogZnVsbFRhYmxlTmFtZSB9KTtcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBjbGllbnQuc2VuZChjb21tYW5kKTtcbiAgICAgICAgXG4gICAgICAgIGlmIChyZXNwb25zZS5UYWJsZSkge1xuICAgICAgICAgIG1hcHBpbmdzLnB1c2goe1xuICAgICAgICAgICAgbG9naWNhbElkOiBgJHt0YWJsZU5hbWV9VGFibGVgLFxuICAgICAgICAgICAgcGh5c2ljYWxSZXNvdXJjZUlkOiBmdWxsVGFibGVOYW1lLFxuICAgICAgICAgICAgcmVzb3VyY2VUeXBlOiAnQVdTOjpEeW5hbW9EQjo6VGFibGUnXG4gICAgICAgICAgfSk7XG4gICAgICAgICAgY29uc29sZS5sb2coYOKchSBGb3VuZCBleGlzdGluZyBEeW5hbW9EQiB0YWJsZTogJHtmdWxsVGFibGVOYW1lfWApO1xuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmxvZyhg4p2MIER5bmFtb0RCIHRhYmxlIG5vdCBmb3VuZDogJHtmdWxsVGFibGVOYW1lfWApO1xuICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiBtYXBwaW5ncztcbiAgfVxuXG4gIHByaXZhdGUgYXN5bmMgY2hlY2tTM0J1Y2tldHMoKTogUHJvbWlzZTxSZXNvdXJjZU1hcHBpbmdbXT4ge1xuICAgIGNvbnN0IGNsaWVudCA9IG5ldyBTM0NsaWVudCh7IHJlZ2lvbjogdGhpcy5yZWdpb24gfSk7XG4gICAgY29uc3QgbWFwcGluZ3M6IFJlc291cmNlTWFwcGluZ1tdID0gW107XG5cbiAgICBjb25zdCBidWNrZXROYW1lID0gYCR7dGhpcy5wcm9qZWN0TmFtZX0tbWVkaWEtJHt0aGlzLmVudmlyb25tZW50fWA7XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgY29tbWFuZCA9IG5ldyBIZWFkQnVja2V0Q29tbWFuZCh7IEJ1Y2tldDogYnVja2V0TmFtZSB9KTtcbiAgICAgIGF3YWl0IGNsaWVudC5zZW5kKGNvbW1hbmQpO1xuICAgICAgXG4gICAgICBtYXBwaW5ncy5wdXNoKHtcbiAgICAgICAgbG9naWNhbElkOiAnTWVkaWFCdWNrZXQnLFxuICAgICAgICBwaHlzaWNhbFJlc291cmNlSWQ6IGJ1Y2tldE5hbWUsXG4gICAgICAgIHJlc291cmNlVHlwZTogJ0FXUzo6UzM6OkJ1Y2tldCdcbiAgICAgIH0pO1xuICAgICAgY29uc29sZS5sb2coYOKchSBGb3VuZCBleGlzdGluZyBTMyBidWNrZXQ6ICR7YnVja2V0TmFtZX1gKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5sb2coYOKdjCBTMyBidWNrZXQgbm90IGZvdW5kOiAke2J1Y2tldE5hbWV9YCk7XG4gICAgfVxuXG4gICAgcmV0dXJuIG1hcHBpbmdzO1xuICB9XG5cbiAgcHJpdmF0ZSBhc3luYyBjaGVja0NvZ25pdG9Vc2VyUG9vbHMoKTogUHJvbWlzZTxSZXNvdXJjZU1hcHBpbmdbXT4ge1xuICAgIGNvbnN0IGNsaWVudCA9IG5ldyBDb2duaXRvSWRlbnRpdHlQcm92aWRlckNsaWVudCh7IHJlZ2lvbjogdGhpcy5yZWdpb24gfSk7XG4gICAgY29uc3QgbWFwcGluZ3M6IFJlc291cmNlTWFwcGluZ1tdID0gW107XG5cbiAgICBjb25zdCB1c2VyUG9vbE5hbWUgPSBgJHt0aGlzLnByb2plY3ROYW1lfS11c2Vycy0ke3RoaXMuZW52aXJvbm1lbnR9YDtcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCBjb21tYW5kID0gbmV3IExpc3RVc2VyUG9vbHNDb21tYW5kKHsgTWF4UmVzdWx0czogNjAgfSk7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGNsaWVudC5zZW5kKGNvbW1hbmQpO1xuICAgICAgXG4gICAgICBjb25zdCB1c2VyUG9vbCA9IHJlc3BvbnNlLlVzZXJQb29scz8uZmluZChwb29sID0+IHBvb2wuTmFtZSA9PT0gdXNlclBvb2xOYW1lKTtcbiAgICAgIFxuICAgICAgaWYgKHVzZXJQb29sICYmIHVzZXJQb29sLklkKSB7XG4gICAgICAgIG1hcHBpbmdzLnB1c2goe1xuICAgICAgICAgIGxvZ2ljYWxJZDogJ1VzZXJQb29sJyxcbiAgICAgICAgICBwaHlzaWNhbFJlc291cmNlSWQ6IHVzZXJQb29sLklkLFxuICAgICAgICAgIHJlc291cmNlVHlwZTogJ0FXUzo6Q29nbml0bzo6VXNlclBvb2wnXG4gICAgICAgIH0pO1xuICAgICAgICBjb25zb2xlLmxvZyhg4pyFIEZvdW5kIGV4aXN0aW5nIENvZ25pdG8gVXNlciBQb29sOiAke3VzZXJQb29sTmFtZX0gKCR7dXNlclBvb2wuSWR9KWApO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmxvZyhg4p2MIENvZ25pdG8gVXNlciBQb29sIG5vdCBmb3VuZDogJHt1c2VyUG9vbE5hbWV9YCk7XG4gICAgfVxuXG4gICAgcmV0dXJuIG1hcHBpbmdzO1xuICB9XG5cbiAgYXN5bmMgZGVwbG95V2l0aEltcG9ydCgpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICBjb25zb2xlLmxvZyhg8J+agCBTdGFydGluZyBkZXBsb3ltZW50IGZvciAke3RoaXMuc3RhY2tOYW1lfS4uLmApO1xuXG4gICAgLy8gQ2hlY2sgZm9yIGV4aXN0aW5nIHJlc291cmNlc1xuICAgIGNvbnN0IGV4aXN0aW5nUmVzb3VyY2VzID0gYXdhaXQgdGhpcy5jaGVja0V4aXN0aW5nUmVzb3VyY2VzKCk7XG5cbiAgICBpZiAoZXhpc3RpbmdSZXNvdXJjZXMubGVuZ3RoID4gMCkge1xuICAgICAgY29uc29sZS5sb2coYFxcbvCfk6YgRm91bmQgJHtleGlzdGluZ1Jlc291cmNlcy5sZW5ndGh9IGV4aXN0aW5nIHJlc291cmNlcyB0byBpbXBvcnQ6YCk7XG4gICAgICBleGlzdGluZ1Jlc291cmNlcy5mb3JFYWNoKHJlc291cmNlID0+IHtcbiAgICAgICAgY29uc29sZS5sb2coYCAgLSAke3Jlc291cmNlLmxvZ2ljYWxJZH06ICR7cmVzb3VyY2UucGh5c2ljYWxSZXNvdXJjZUlkfWApO1xuICAgICAgfSk7XG5cbiAgICAgIC8vIENyZWF0ZSByZXNvdXJjZSBtYXBwaW5nIGZpbGUgZm9yIENESyBpbXBvcnRcbiAgICAgIGNvbnN0IG1hcHBpbmdGaWxlID0gdGhpcy5jcmVhdGVSZXNvdXJjZU1hcHBpbmdGaWxlKGV4aXN0aW5nUmVzb3VyY2VzKTtcbiAgICAgIFxuICAgICAgY29uc29sZS5sb2coJ1xcbvCflIQgUGVyZm9ybWluZyBDREsgaW1wb3J0Li4uJyk7XG4gICAgICB0cnkge1xuICAgICAgICBleGVjU3luYyhgY2RrIGltcG9ydCAke3RoaXMuc3RhY2tOYW1lfSAtLXJlc291cmNlLW1hcHBpbmcgJHttYXBwaW5nRmlsZX1gLCB7XG4gICAgICAgICAgc3RkaW86ICdpbmhlcml0JyxcbiAgICAgICAgICBjd2Q6IHByb2Nlc3MuY3dkKClcbiAgICAgICAgfSk7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUgSW1wb3J0IGNvbXBsZXRlZCBzdWNjZXNzZnVsbHknKTtcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfimqDvuI8gIEltcG9ydCBmYWlsZWQsIHByb2NlZWRpbmcgd2l0aCByZWd1bGFyIGRlcGxveW1lbnQuLi4nKTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBEZXBsb3kgdGhlIHN0YWNrXG4gICAgY29uc29sZS5sb2coJ1xcbvCfj5fvuI8gIERlcGxveWluZyBzdGFjay4uLicpO1xuICAgIGV4ZWNTeW5jKGBjZGsgZGVwbG95ICR7dGhpcy5zdGFja05hbWV9IC0tcmVxdWlyZS1hcHByb3ZhbCBuZXZlcmAsIHtcbiAgICAgIHN0ZGlvOiAnaW5oZXJpdCcsXG4gICAgICBjd2Q6IHByb2Nlc3MuY3dkKClcbiAgICB9KTtcblxuICAgIGNvbnNvbGUubG9nKCfinIUgRGVwbG95bWVudCBjb21wbGV0ZWQgc3VjY2Vzc2Z1bGx5IScpO1xuICB9XG5cbiAgcHJpdmF0ZSBjcmVhdGVSZXNvdXJjZU1hcHBpbmdGaWxlKHJlc291cmNlczogUmVzb3VyY2VNYXBwaW5nW10pOiBzdHJpbmcge1xuICAgIGNvbnN0IG1hcHBpbmdDb250ZW50ID0gcmVzb3VyY2VzLm1hcChyZXNvdXJjZSA9PiBcbiAgICAgIGAke3Jlc291cmNlLmxvZ2ljYWxJZH09JHtyZXNvdXJjZS5waHlzaWNhbFJlc291cmNlSWR9YFxuICAgICkuam9pbignXFxuJyk7XG5cbiAgICBjb25zdCBmaWxlbmFtZSA9IGAvdG1wL3Jlc291cmNlLW1hcHBpbmctJHtEYXRlLm5vdygpfS50eHRgO1xuICAgIHJlcXVpcmUoJ2ZzJykud3JpdGVGaWxlU3luYyhmaWxlbmFtZSwgbWFwcGluZ0NvbnRlbnQpO1xuICAgIFxuICAgIHJldHVybiBmaWxlbmFtZTtcbiAgfVxufVxuXG4vLyBNYWluIGV4ZWN1dGlvblxuYXN5bmMgZnVuY3Rpb24gbWFpbigpIHtcbiAgY29uc3QgbWFuYWdlciA9IG5ldyBEZXBsb3ltZW50TWFuYWdlcigpO1xuICBcbiAgdHJ5IHtcbiAgICBhd2FpdCBtYW5hZ2VyLmRlcGxveVdpdGhJbXBvcnQoKTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCfinYwgRGVwbG95bWVudCBmYWlsZWQ6JywgZXJyb3IpO1xuICAgIHByb2Nlc3MuZXhpdCgxKTtcbiAgfVxufVxuXG5pZiAocmVxdWlyZS5tYWluID09PSBtb2R1bGUpIHtcbiAgbWFpbigpO1xufVxuXG5leHBvcnQgeyBEZXBsb3ltZW50TWFuYWdlciB9O1xuIl19