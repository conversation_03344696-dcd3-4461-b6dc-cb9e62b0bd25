#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to generate API documentation
 */

const { spawn } = require('child_process');
const path = require('path');

async function main() {
    console.log('🚀 Starting API documentation generation...\n');

    try {
        // Build the TypeScript files first
        console.log('📦 Building TypeScript files...');
        await runCommand('npm', ['run', 'build']);

        // Run the documentation generator
        console.log('📝 Generating API documentation...');
        await runCommand('node', ['-r', 'ts-node/register', 'src/documentation/cli.ts', 'generate']);

        console.log('\n✅ API documentation generated successfully!');
        console.log('📄 Check the following locations:');
        console.log('   - OpenAPI spec: docs/api/openapi.json');
        console.log('   - Swagger spec: docs/api/swagger.json');
        console.log('   - Markdown docs: docs/api/API_DOCUMENTATION.md');
        console.log('   - Kiro docs: .kiro/api-docs/api-documentation.json');
        console.log('   - Routes summary: .kiro/api-docs/routes-summary.md');

    } catch (error) {
        console.error('❌ Error generating documentation:', error.message);
        process.exit(1);
    }
}

function runCommand(command, args, options = {}) {
    return new Promise((resolve, reject) => {
        const child = spawn(command, args, {
            stdio: 'inherit',
            cwd: path.join(__dirname, '..'),
            ...options
        });

        child.on('close', (code) => {
            if (code === 0) {
                resolve();
            } else {
                reject(new Error(`Command failed with exit code ${code}`));
            }
        });

        child.on('error', (error) => {
            reject(error);
        });
    });
}

if (require.main === module) {
    main();
}