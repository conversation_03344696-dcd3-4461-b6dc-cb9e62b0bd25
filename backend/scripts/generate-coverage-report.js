#!/usr/bin/env node

/**
 * Comprehensive API Route Coverage Report Generator
 * 
 * This script generates a detailed coverage report showing which API routes
 * are tested and which need additional coverage.
 */

const fs = require('fs');
const path = require('path');

// Load API documentation
const apiDocsPath = path.join(__dirname, '../../.kiro/api-docs/api-documentation.json');
const testsDir = path.join(__dirname, '../tests');

function loadApiDocumentation() {
    try {
        const apiDocs = JSON.parse(fs.readFileSync(apiDocsPath, 'utf8'));
        return apiDocs.routes || [];
    } catch (error) {
        console.error('Error loading API documentation:', error.message);
        return [];
    }
}

function getExistingTestFiles() {
    try {
        const files = fs.readdirSync(testsDir);
        return files.filter(file => file.endsWith('.test.ts') || file.endsWith('.test.js'));
    } catch (error) {
        console.error('Error reading test directory:', error.message);
        return [];
    }
}

function analyzeTestFile(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');

        // Extract API routes being tested with more sophisticated patterns
        const routePatterns = [
            /['"`]\/[a-zA-Z0-9\-_\/{}]+['"`]/g,  // String literals with paths
            /\$\{API_BASE_URL\}\/([a-zA-Z0-9\-_\/{}]+)/g,  // Template literals
            /axios\.(get|post|put|delete)\(['"`]([^'"`]+)['"`]/g,  // Axios calls
        ];

        const testedRoutes = new Set();
        const httpMethods = new Set();

        routePatterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(content)) !== null) {
                if (match[2]) {
                    // Axios method calls
                    httpMethods.add(match[1].toUpperCase());
                    const route = match[2].replace(/\$\{API_BASE_URL\}/, '');
                    if (route.startsWith('/')) {
                        testedRoutes.add(route);
                    }
                } else {
                    // String literals
                    const route = (match[1] || match[0]).replace(/['"`]/g, '').replace(/\$\{API_BASE_URL\}/, '');
                    if (route.startsWith('/')) {
                        testedRoutes.add(route);
                    }
                }
            }
        });

        // Extract test descriptions to understand what's being tested
        const testDescriptions = [];
        const descriptionPatterns = [
            /describe\(['"`]([^'"`]+)['"`]/g,
            /it\(['"`]([^'"`]+)['"`]/g,
            /test\(['"`]([^'"`]+)['"`]/g,
        ];

        descriptionPatterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(content)) !== null) {
                testDescriptions.push(match[1]);
            }
        });

        return {
            routes: Array.from(testedRoutes),
            methods: Array.from(httpMethods),
            descriptions: testDescriptions,
            hasAuthTests: /auth|token|signin|signup|login|Bearer/i.test(content),
            hasErrorTests: /error|fail|invalid|unauthorized|forbidden|400|401|403|404|500/i.test(content),
            hasValidationTests: /validat|schema|required|missing|invalid.*data/i.test(content),
            hasWorkflowTests: /workflow|integration|complete.*flow/i.test(content),
            lineCount: content.split('\n').length
        };
    } catch (error) {
        console.error(`Error analyzing test file ${filePath}:`, error.message);
        return {
            routes: [],
            methods: [],
            descriptions: [],
            hasAuthTests: false,
            hasErrorTests: false,
            hasValidationTests: false,
            hasWorkflowTests: false,
            lineCount: 0
        };
    }
}

function categorizeRoutes(routes) {
    const categories = {
        auth: [],
        users: [],
        posts: [],
        channels: [],
        media: [],
        notifications: [],
        reflexes: [],
        health: [],
        xbox: [],
        twitch: [],
        kick: []
    };

    routes.forEach(route => {
        const path = route.path.toLowerCase();
        if (path.startsWith('/auth')) categories.auth.push(route);
        else if (path.startsWith('/users')) categories.users.push(route);
        else if (path.startsWith('/posts')) categories.posts.push(route);
        else if (path.startsWith('/channels')) categories.channels.push(route);
        else if (path.startsWith('/media')) categories.media.push(route);
        else if (path.includes('notification') || path.startsWith('/device-token')) categories.notifications.push(route);
        else if (path.startsWith('/reflexes')) categories.reflexes.push(route);
        else if (path.startsWith('/health')) categories.health.push(route);
        else if (path.startsWith('/xbox')) categories.xbox.push(route);
        else if (path.startsWith('/twitch')) categories.twitch.push(route);
        else if (path.startsWith('/kick')) categories.kick.push(route);
    });

    return categories;
}

function generateDetailedCoverageReport() {
    console.log('INFO: Generating Comprehensive API Route Coverage Report...');

    const routes = loadApiDocumentation();
    const testFiles = getExistingTestFiles();

    console.log('INFO: Analysis Summary:');
    console.log(`INFO:   Total API Routes: ${routes.length}`);
    console.log(`INFO:   Test Files Found: ${testFiles.length}`);

    // Analyze existing test files
    const testAnalysis = {};
    testFiles.forEach(file => {
        const filePath = path.join(testsDir, file);
        testAnalysis[file] = analyzeTestFile(filePath);
    });

    // Categorize routes
    const routeCategories = categorizeRoutes(routes);

    // Generate detailed coverage analysis
    const coverage = {
        total: routes.length,
        covered: 0,
        uncovered: [],
        partialCoverage: [],
        fullCoverage: [],
        missingTestFiles: [],
        gapsByCategory: {},
        methodCoverage: {},
        qualitativeAnalysis: {}
    };

    // Analyze coverage by category
    Object.entries(routeCategories).forEach(([category, categoryRoutes]) => {
        const testFile = `${category}.test.ts`;
        const hasTestFile = testFiles.includes(testFile);

        coverage.gapsByCategory[category] = {
            routes: categoryRoutes,
            hasTestFile,
            testFile,
            coverage: 'none',
            gaps: [],
            coveredRoutes: [],
            methodsCovered: new Set(),
            qualityScore: 0
        };

        if (!hasTestFile && categoryRoutes.length > 0) {
            coverage.missingTestFiles.push({
                category,
                testFile,
                routeCount: categoryRoutes.length,
                routes: categoryRoutes
            });
        }

        // Analyze specific route coverage
        categoryRoutes.forEach(route => {
            const routeKey = `${route.method} ${route.path}`;
            let isCovered = false;
            let coverageQuality = 0;

            if (hasTestFile) {
                const analysis = testAnalysis[testFile];
                if (analysis) {
                    // Check if route path is mentioned in tests
                    const pathCovered = analysis.routes.some(testedRoute => {
                        const routePath = route.path.replace(/\{[^}]+\}/g, ''); // Remove path parameters
                        return testedRoute.includes(routePath.split('/')[1]) ||
                            routePath.includes(testedRoute.split('/')[1]);
                    });

                    // Check if HTTP method is tested
                    const methodCovered = analysis.methods.includes(route.method);

                    isCovered = pathCovered || methodCovered;

                    if (isCovered) {
                        coverage.gapsByCategory[category].coveredRoutes.push(route);
                        coverage.gapsByCategory[category].methodsCovered.add(route.method);

                        // Calculate quality score
                        if (analysis.hasAuthTests) coverageQuality += 1;
                        if (analysis.hasErrorTests) coverageQuality += 1;
                        if (analysis.hasValidationTests) coverageQuality += 1;
                        if (analysis.hasWorkflowTests) coverageQuality += 1;
                    }
                }
            }

            if (isCovered) {
                coverage.covered++;
                coverage.fullCoverage.push(routeKey);
            } else {
                coverage.uncovered.push(routeKey);
                coverage.gapsByCategory[category].gaps.push(route);
            }
        });

        // Calculate category coverage percentage
        const categoryTotal = categoryRoutes.length;
        const categoryCovered = coverage.gapsByCategory[category].coveredRoutes.length;
        coverage.gapsByCategory[category].coverage = categoryTotal > 0 ?
            ((categoryCovered / categoryTotal) * 100).toFixed(1) + '%' : '0%';

        // Calculate quality score
        if (hasTestFile) {
            const analysis = testAnalysis[testFile];
            let qualityScore = 0;
            if (analysis.hasAuthTests) qualityScore += 25;
            if (analysis.hasErrorTests) qualityScore += 25;
            if (analysis.hasValidationTests) qualityScore += 25;
            if (analysis.hasWorkflowTests) qualityScore += 25;
            coverage.gapsByCategory[category].qualityScore = qualityScore;
        }
    });

    // Method coverage analysis
    const allMethods = ['GET', 'POST', 'PUT', 'DELETE'];
    allMethods.forEach(method => {
        const methodRoutes = routes.filter(r => r.method === method);
        const coveredMethodRoutes = coverage.fullCoverage.filter(r => r.startsWith(method));
        coverage.methodCoverage[method] = {
            total: methodRoutes.length,
            covered: coveredMethodRoutes.length,
            percentage: methodRoutes.length > 0 ?
                ((coveredMethodRoutes.length / methodRoutes.length) * 100).toFixed(1) + '%' : '0%'
        };
    });

    // Generate HTML report
    const htmlReport = generateHTMLReport(coverage, testAnalysis, routeCategories);

    // Generate console report
    generateConsoleReport(coverage, testAnalysis);

    // Save reports
    const timestamp = new Date().toISOString();
    const reportData = {
        timestamp,
        summary: {
            totalRoutes: coverage.total,
            coveredRoutes: coverage.covered,
            uncoveredRoutes: coverage.uncovered.length,
            coveragePercentage: ((coverage.covered / coverage.total) * 100).toFixed(1) + '%'
        },
        coverage,
        testAnalysis,
        routeCategories
    };

    const jsonReportPath = path.join(__dirname, '../API_ROUTE_COVERAGE_REPORT.json');
    const htmlReportPath = path.join(__dirname, '../API_ROUTE_COVERAGE_REPORT.html');

    fs.writeFileSync(jsonReportPath, JSON.stringify(reportData, null, 2));
    fs.writeFileSync(htmlReportPath, htmlReport);

    console.log('INFO: Reports Generated:');
    console.log(`INFO:   JSON Report: ${jsonReportPath}`);
    console.log(`INFO:   HTML Report: ${htmlReportPath}`);

    return coverage;
}

function generateConsoleReport(coverage, testAnalysis) {
    console.log('INFO: COVERAGE SUMMARY');
    console.log('='.repeat(60));
    console.log(`INFO: Total API Routes: ${coverage.total}`);
    console.log(`INFO: Covered Routes: ${coverage.covered}`);
    console.log(`INFO: Uncovered Routes: ${coverage.uncovered.length}`);
    console.log(`INFO: Coverage Percentage: ${((coverage.covered / coverage.total) * 100).toFixed(1)}%`);

    // Method coverage
    console.log('INFO: HTTP METHOD COVERAGE');
    console.log('='.repeat(60));
    Object.entries(coverage.methodCoverage).forEach(([method, data]) => {
        console.log(`INFO: ${method.padEnd(6)}: ${data.covered}/${data.total} (${data.percentage})`);
    });

    // Category coverage
    console.log('INFO: CATEGORY COVERAGE');
    console.log('='.repeat(60));
    Object.entries(coverage.gapsByCategory).forEach(([category, info]) => {
        const status = info.hasTestFile ? 'Complete' : 'Missing';
        const quality = info.qualityScore ? ` (Quality: ${info.qualityScore}%)` : '';
        console.log(`INFO: ${status} ${category.toUpperCase().padEnd(12)}: ${info.coverage}${quality}`);

        if (info.gaps.length > 0) {
            console.log(`WARN:    Missing coverage for ${info.gaps.length} routes:`);
            info.gaps.slice(0, 3).forEach(route => {
                console.log(`WARN:    • ${route.method} ${route.path}`);
            });
            if (info.gaps.length > 3) {
                console.log(`WARN:    • ... and ${info.gaps.length - 3} more`);
            }
        }
    });

    // Recommendations
    console.log('INFO: RECOMMENDATIONS');
    console.log('='.repeat(60));

    const recommendations = [];

    if (coverage.missingTestFiles.length > 0) {
        recommendations.push(`Create ${coverage.missingTestFiles.length} missing test files`);
    }

    const lowCoverageCategories = Object.entries(coverage.gapsByCategory)
        .filter(([_, info]) => parseFloat(info.coverage) < 80)
        .map(([category]) => category);

    if (lowCoverageCategories.length > 0) {
        recommendations.push(`Improve coverage for: ${lowCoverageCategories.join(', ')}`);
    }

    const lowQualityCategories = Object.entries(coverage.gapsByCategory)
        .filter(([_, info]) => info.qualityScore < 75 && info.hasTestFile)
        .map(([category]) => category);

    if (lowQualityCategories.length > 0) {
        recommendations.push(`Enhance test quality for: ${lowQualityCategories.join(', ')}`);
    }

    recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. ${rec}`);
    });
}

function generateHTMLReport(coverage, testAnalysis, routeCategories) {
    const timestamp = new Date().toISOString();
    const coveragePercentage = ((coverage.covered / coverage.total) * 100).toFixed(1);

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Route Coverage Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; }
        .metric h3 { margin: 0 0 10px 0; color: #333; }
        .metric .value { font-size: 2em; font-weight: bold; color: #007bff; }
        .category { margin-bottom: 30px; }
        .category h3 { background: #007bff; color: white; padding: 10px; margin: 0; border-radius: 4px 4px 0 0; }
        .category-content { border: 1px solid #ddd; border-top: none; padding: 15px; border-radius: 0 0 4px 4px; }
        .route { padding: 8px; margin: 4px 0; border-radius: 4px; }
        .route.covered { background: #d4edda; border-left: 4px solid #28a745; }
        .route.uncovered { background: #f8d7da; border-left: 4px solid #dc3545; }
        .method { font-weight: bold; color: #495057; }
        .path { font-family: monospace; }
        .description { color: #6c757d; font-size: 0.9em; }
        .progress-bar { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #28a745, #20c997); transition: width 0.3s ease; }
        .test-quality { display: flex; gap: 10px; margin-top: 10px; }
        .quality-badge { padding: 4px 8px; border-radius: 12px; font-size: 0.8em; }
        .quality-badge.good { background: #d4edda; color: #155724; }
        .quality-badge.missing { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 API Route Coverage Report</h1>
            <p>Generated on ${new Date(timestamp).toLocaleString()}</p>
        </div>
        
        <div class="summary">
            <div class="metric">
                <h3>Total Routes</h3>
                <div class="value">${coverage.total}</div>
            </div>
            <div class="metric">
                <h3>Covered Routes</h3>
                <div class="value">${coverage.covered}</div>
            </div>
            <div class="metric">
                <h3>Coverage</h3>
                <div class="value">${coveragePercentage}%</div>
            </div>
            <div class="metric">
                <h3>Test Files</h3>
                <div class="value">${Object.keys(testAnalysis).length}</div>
            </div>
        </div>
        
        <div class="progress-bar">
            <div class="progress-fill" style="width: ${coveragePercentage}%"></div>
        </div>
        
        ${Object.entries(coverage.gapsByCategory).map(([category, info]) => `
            <div class="category">
                <h3>${category.toUpperCase()} - ${info.coverage} Coverage</h3>
                <div class="category-content">
                    ${info.hasTestFile ? `
                        <div class="test-quality">
                            <span class="quality-badge ${info.qualityScore >= 75 ? 'good' : 'missing'}">
                                Quality Score: ${info.qualityScore}%
                            </span>
                        </div>
                    ` : '<p style="color: #dc3545;">❌ No test file found</p>'}
                    
                    ${info.routes.map(route => {
        const isCovered = info.coveredRoutes.includes(route);
        return `
                            <div class="route ${isCovered ? 'covered' : 'uncovered'}">
                                <span class="method">${route.method}</span>
                                <span class="path">${route.path}</span>
                                <div class="description">${route.description}</div>
                            </div>
                        `;
    }).join('')}
                </div>
            </div>
        `).join('')}
    </div>
</body>
</html>
    `;
}

// Run the analysis
if (require.main === module) {
    generateDetailedCoverageReport();
}

module.exports = { generateDetailedCoverageReport };