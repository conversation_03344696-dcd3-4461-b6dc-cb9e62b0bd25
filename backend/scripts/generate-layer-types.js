#!/usr/bin/env node

/**
 * Generate layer types - utilities are now directly in the layer
 * This script ensures the layer is properly built and ready for deployment
 */

const fs = require('fs');
const path = require('path');

const LAYER_DIR = path.join(__dirname, '../src/layers/utils-layer/nodejs');
const DIST_DIR = path.join(__dirname, '../src/layers/dist/utils-layer/nodejs');

// Ensure layer directory exists
if (!fs.existsSync(LAYER_DIR)) {
  console.error('Layer directory does not exist:', LAYER_DIR);
  process.exit(1);
}

// Check if layer has been built
if (!fs.existsSync(DIST_DIR)) {
  console.error('Layer has not been built. Please run "npm run build" in the layers directory first.');
  process.exit(1);
}

// Get all TypeScript files in layer directory (excluding .d.ts files)
const layerFiles = fs.readdirSync(LAYER_DIR)
  .filter(file => file.endsWith('.ts') && !file.endsWith('.d.ts'))
  .map(file => path.basename(file, '.ts'));

console.log('Found layer modules:', layerFiles);

// Verify compiled files exist
const missingFiles = [];
layerFiles.forEach(moduleName => {
  const compiledFile = path.join(DIST_DIR, `${moduleName}.js`);
  const typeFile = path.join(DIST_DIR, `${moduleName}.d.ts`);

  if (!fs.existsSync(compiledFile)) {
    missingFiles.push(`${moduleName}.js`);
  }
  if (!fs.existsSync(typeFile)) {
    missingFiles.push(`${moduleName}.d.ts`);
  }
});

if (missingFiles.length > 0) {
  console.error('Missing compiled files:', missingFiles);
  console.error('Please run "npm run build" in the layers directory.');
  process.exit(1);
}

console.log(`✅ Layer is properly built and ready for deployment`);
console.log(`Layer source: ${LAYER_DIR}`);
console.log(`Layer dist: ${DIST_DIR}`);
