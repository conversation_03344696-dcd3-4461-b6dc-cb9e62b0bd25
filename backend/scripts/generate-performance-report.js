#!/usr/bin/env node

/**
 * Performance Report Generator
 * Generates comprehensive performance reports from test execution data
 */

const fs = require('fs');
const path = require('path');

// Configuration
const REPORTS_DIR = path.join(__dirname, '..', 'test-reports');
const PERFORMANCE_DIR = path.join(__dirname, '..', 'performance-reports');
const TIMESTAMP = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);

// Ensure directories exist
function ensureDirectories() {
    if (!fs.existsSync(REPORTS_DIR)) {
        fs.mkdirSync(REPORTS_DIR, { recursive: true });
    }
    if (!fs.existsSync(PERFORMANCE_DIR)) {
        fs.mkdirSync(PERFORMANCE_DIR, { recursive: true });
    }
}

// Extract performance metrics from test logs
function extractPerformanceMetrics(logFile) {
    if (!fs.existsSync(logFile)) {
        return null;
    }

    const content = fs.readFileSync(logFile, 'utf8');
    const metrics = {
        routes: [],
        totalRequests: 0,
        averageResponseTime: 0,
        slowRoutes: [],
        errorRate: 0
    };

    // Extract route timing information
    const routeTimingRegex = /\[PERF\] Route (GET|POST|PUT|DELETE) (\/[^\s]+) completed in (\d+)ms/g;
    let match;
    const routeData = {};

    while ((match = routeTimingRegex.exec(content)) !== null) {
        const [, method, route, time] = match;
        const key = `${method} ${route}`;

        if (!routeData[key]) {
            routeData[key] = {
                method,
                route,
                times: [],
                count: 0,
                totalTime: 0
            };
        }

        const timeMs = parseInt(time);
        routeData[key].times.push(timeMs);
        routeData[key].count++;
        routeData[key].totalTime += timeMs;
        metrics.totalRequests++;
    }

    // Calculate route statistics
    for (const [key, data] of Object.entries(routeData)) {
        const times = data.times.sort((a, b) => a - b);
        const routeMetric = {
            route: data.route,
            method: data.method,
            count: data.count,
            totalTime: data.totalTime,
            averageTime: Math.round(data.totalTime / data.count),
            minTime: Math.min(...times),
            maxTime: Math.max(...times),
            medianTime: times[Math.floor(times.length / 2)],
            p95Time: times[Math.floor(times.length * 0.95)],
            p99Time: times[Math.floor(times.length * 0.99)]
        };

        metrics.routes.push(routeMetric);

        // Identify slow routes (>1000ms average)
        if (routeMetric.averageTime > 1000) {
            metrics.slowRoutes.push(routeMetric);
        }
    }

    // Calculate overall metrics
    if (metrics.routes.length > 0) {
        const totalTime = metrics.routes.reduce((sum, route) => sum + route.totalTime, 0);
        metrics.averageResponseTime = Math.round(totalTime / metrics.totalRequests);
    }

    // Extract error information
    const errorRegex = /\[ERROR\]/g;
    const errors = content.match(errorRegex) || [];
    metrics.errorRate = metrics.totalRequests > 0 ? (errors.length / metrics.totalRequests) * 100 : 0;

    return metrics;
}

// Generate HTML performance report
function generateHTMLReport(metrics, outputFile) {
    const html = `
<!DOCTYPE html>
<html>
<head>
    <title>GameFlex Performance Report</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background-color: #f5f5f5; 
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            border-radius: 8px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
            overflow: hidden;
        }
        .header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            padding: 30px; 
            text-align: center; 
        }
        .header h1 { margin: 0; font-size: 2.5em; }
        .header p { margin: 10px 0 0 0; opacity: 0.9; }
        .content { padding: 30px; }
        .metrics-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
            gap: 20px; 
            margin-bottom: 30px; 
        }
        .metric-card { 
            background: #f8f9fa; 
            padding: 20px; 
            border-radius: 8px; 
            border-left: 4px solid #667eea; 
            text-align: center;
        }
        .metric-value { 
            font-size: 2em; 
            font-weight: bold; 
            color: #333; 
            margin-bottom: 5px; 
        }
        .metric-label { 
            color: #666; 
            font-size: 0.9em; 
            text-transform: uppercase; 
            letter-spacing: 1px; 
        }
        .section { 
            margin: 30px 0; 
        }
        .section h2 { 
            color: #333; 
            border-bottom: 2px solid #667eea; 
            padding-bottom: 10px; 
        }
        table { 
            width: 100%; 
            border-collapse: collapse; 
            margin-top: 15px; 
        }
        th, td { 
            padding: 12px; 
            text-align: left; 
            border-bottom: 1px solid #ddd; 
        }
        th { 
            background-color: #f8f9fa; 
            font-weight: 600; 
            color: #333; 
        }
        tr:hover { 
            background-color: #f8f9fa; 
        }
        .slow-route { 
            background-color: #fff3cd !important; 
            border-left: 3px solid #ffc107; 
        }
        .fast-route { 
            background-color: #d4edda !important; 
            border-left: 3px solid #28a745; 
        }
        .method-get { color: #28a745; font-weight: bold; }
        .method-post { color: #007bff; font-weight: bold; }
        .method-put { color: #ffc107; font-weight: bold; }
        .method-delete { color: #dc3545; font-weight: bold; }
        .recommendations { 
            background: #e7f3ff; 
            border: 1px solid #b3d9ff; 
            border-radius: 8px; 
            padding: 20px; 
            margin-top: 20px; 
        }
        .recommendations h3 { 
            color: #0066cc; 
            margin-top: 0; 
        }
        .recommendations ul { 
            margin: 10px 0; 
            padding-left: 20px; 
        }
        .recommendations li { 
            margin: 8px 0; 
            line-height: 1.5; 
        }
        .timestamp { 
            color: #666; 
            font-size: 0.9em; 
            text-align: center; 
            margin-top: 30px; 
            padding-top: 20px; 
            border-top: 1px solid #eee; 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Performance Report</h1>
            <p>GameFlex Backend Test Infrastructure</p>
            <p>Generated: ${new Date().toLocaleString()}</p>
        </div>
        
        <div class="content">
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value">${metrics.totalRequests}</div>
                    <div class="metric-label">Total Requests</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${metrics.averageResponseTime}ms</div>
                    <div class="metric-label">Avg Response Time</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${metrics.routes.length}</div>
                    <div class="metric-label">Routes Tested</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${metrics.errorRate.toFixed(1)}%</div>
                    <div class="metric-label">Error Rate</div>
                </div>
            </div>
            
            <div class="section">
                <h2>📊 Route Performance Details</h2>
                <table>
                    <thead>
                        <tr>
                            <th>Method</th>
                            <th>Route</th>
                            <th>Requests</th>
                            <th>Avg Time</th>
                            <th>Min Time</th>
                            <th>Max Time</th>
                            <th>P95 Time</th>
                            <th>P99 Time</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${metrics.routes
            .sort((a, b) => b.averageTime - a.averageTime)
            .map(route => `
                                <tr class="${route.averageTime > 1000 ? 'slow-route' : route.averageTime < 200 ? 'fast-route' : ''}">
                                    <td><span class="method-${route.method.toLowerCase()}">${route.method}</span></td>
                                    <td><code>${route.route}</code></td>
                                    <td>${route.count}</td>
                                    <td>${route.averageTime}ms</td>
                                    <td>${route.minTime}ms</td>
                                    <td>${route.maxTime}ms</td>
                                    <td>${route.p95Time}ms</td>
                                    <td>${route.p99Time}ms</td>
                                </tr>
                            `).join('')}
                    </tbody>
                </table>
            </div>
            
            ${metrics.slowRoutes.length > 0 ? `
            <div class="section">
                <h2>⚠️ Slow Routes (>1000ms)</h2>
                <table>
                    <thead>
                        <tr>
                            <th>Method</th>
                            <th>Route</th>
                            <th>Avg Time</th>
                            <th>Max Time</th>
                            <th>Requests</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${metrics.slowRoutes
                .sort((a, b) => b.averageTime - a.averageTime)
                .map(route => `
                                <tr class="slow-route">
                                    <td><span class="method-${route.method.toLowerCase()}">${route.method}</span></td>
                                    <td><code>${route.route}</code></td>
                                    <td>${route.averageTime}ms</td>
                                    <td>${route.maxTime}ms</td>
                                    <td>${route.count}</td>
                                </tr>
                            `).join('')}
                    </tbody>
                </table>
            </div>
            ` : ''}
            
            <div class="recommendations">
                <h3>💡 Performance Recommendations</h3>
                <ul>
                    ${metrics.slowRoutes.length > 0 ?
            `<li><strong>Optimize slow routes:</strong> ${metrics.slowRoutes.length} routes are taking longer than 1000ms on average. Consider adding caching, database optimization, or async processing.</li>` :
            '<li><strong>Good performance:</strong> No routes are significantly slow (>1000ms average).</li>'
        }
                    ${metrics.averageResponseTime > 500 ?
            '<li><strong>Overall response time:</strong> Average response time is above 500ms. Consider implementing response caching and database query optimization.</li>' :
            '<li><strong>Good response times:</strong> Average response time is within acceptable limits.</li>'
        }
                    ${metrics.errorRate > 5 ?
            '<li><strong>High error rate:</strong> Error rate is above 5%. Review error logs and implement better error handling.</li>' :
            '<li><strong>Low error rate:</strong> Error rate is within acceptable limits.</li>'
        }
                    <li><strong>Monitoring:</strong> Continue monitoring these metrics in production and set up alerts for performance degradation.</li>
                    <li><strong>Load testing:</strong> Consider running load tests to understand performance under higher traffic.</li>
                </ul>
            </div>
            
            <div class="timestamp">
                Report generated by GameFlex Enhanced Test Infrastructure<br>
                Timestamp: ${TIMESTAMP}
            </div>
        </div>
    </div>
</body>
</html>`;

    fs.writeFileSync(outputFile, html);
    console.log(`✅ HTML performance report generated: ${outputFile}`);
}

// Generate JSON performance report
function generateJSONReport(metrics, outputFile) {
    const report = {
        timestamp: new Date().toISOString(),
        summary: {
            totalRequests: metrics.totalRequests,
            averageResponseTime: metrics.averageResponseTime,
            errorRate: metrics.errorRate,
            routeCount: metrics.routes.length,
            slowRouteCount: metrics.slowRoutes.length
        },
        routes: metrics.routes,
        slowRoutes: metrics.slowRoutes,
        recommendations: generateRecommendations(metrics)
    };

    fs.writeFileSync(outputFile, JSON.stringify(report, null, 2));
    console.log(`✅ JSON performance report generated: ${outputFile}`);
}

// Generate performance recommendations
function generateRecommendations(metrics) {
    const recommendations = [];

    if (metrics.slowRoutes.length > 0) {
        recommendations.push({
            type: 'performance',
            priority: 'high',
            message: `${metrics.slowRoutes.length} routes are taking longer than 1000ms on average`,
            routes: metrics.slowRoutes.map(r => `${r.method} ${r.route}`),
            suggestion: 'Consider adding caching, database optimization, or async processing'
        });
    }

    if (metrics.averageResponseTime > 500) {
        recommendations.push({
            type: 'performance',
            priority: 'medium',
            message: 'Average response time is above 500ms',
            suggestion: 'Implement response caching and database query optimization'
        });
    }

    if (metrics.errorRate > 5) {
        recommendations.push({
            type: 'reliability',
            priority: 'high',
            message: `Error rate is ${metrics.errorRate.toFixed(1)}% (above 5% threshold)`,
            suggestion: 'Review error logs and implement better error handling'
        });
    }

    recommendations.push({
        type: 'monitoring',
        priority: 'low',
        message: 'Continue performance monitoring',
        suggestion: 'Set up alerts for performance degradation and consider load testing'
    });

    return recommendations;
}

// Main execution
function main() {
    console.log('🚀 Generating performance report...');

    ensureDirectories();

    // Find the most recent test log files
    const logFiles = fs.readdirSync(REPORTS_DIR)
        .filter(file => file.startsWith('test_') && file.endsWith('.log'))
        .map(file => path.join(REPORTS_DIR, file))
        .sort((a, b) => fs.statSync(b).mtime - fs.statSync(a).mtime);

    if (logFiles.length === 0) {
        console.log('❌ No test log files found in', REPORTS_DIR);
        console.log('Run tests with performance monitoring enabled first.');
        process.exit(1);
    }

    console.log(`📊 Analyzing ${logFiles.length} test log files...`);

    // Aggregate metrics from all log files
    const aggregatedMetrics = {
        routes: [],
        totalRequests: 0,
        averageResponseTime: 0,
        slowRoutes: [],
        errorRate: 0
    };

    const routeData = {};
    let totalTime = 0;
    let totalErrors = 0;

    for (const logFile of logFiles) {
        console.log(`📝 Processing: ${path.basename(logFile)}`);
        const metrics = extractPerformanceMetrics(logFile);

        if (metrics) {
            aggregatedMetrics.totalRequests += metrics.totalRequests;
            totalTime += metrics.routes.reduce((sum, route) => sum + route.totalTime, 0);

            // Merge route data
            for (const route of metrics.routes) {
                const key = `${route.method} ${route.route}`;
                if (!routeData[key]) {
                    routeData[key] = {
                        method: route.method,
                        route: route.route,
                        times: [],
                        count: 0,
                        totalTime: 0
                    };
                }

                // Add individual times for more accurate statistics
                const avgTime = route.averageTime;
                for (let i = 0; i < route.count; i++) {
                    routeData[key].times.push(avgTime);
                }
                routeData[key].count += route.count;
                routeData[key].totalTime += route.totalTime;
            }
        }
    }

    // Calculate final route statistics
    for (const [key, data] of Object.entries(routeData)) {
        const times = data.times.sort((a, b) => a - b);
        const routeMetric = {
            route: data.route,
            method: data.method,
            count: data.count,
            totalTime: data.totalTime,
            averageTime: Math.round(data.totalTime / data.count),
            minTime: Math.min(...times),
            maxTime: Math.max(...times),
            medianTime: times[Math.floor(times.length / 2)],
            p95Time: times[Math.floor(times.length * 0.95)] || times[times.length - 1],
            p99Time: times[Math.floor(times.length * 0.99)] || times[times.length - 1]
        };

        aggregatedMetrics.routes.push(routeMetric);

        if (routeMetric.averageTime > 1000) {
            aggregatedMetrics.slowRoutes.push(routeMetric);
        }
    }

    // Calculate overall metrics
    if (aggregatedMetrics.totalRequests > 0) {
        aggregatedMetrics.averageResponseTime = Math.round(totalTime / aggregatedMetrics.totalRequests);
        aggregatedMetrics.errorRate = (totalErrors / aggregatedMetrics.totalRequests) * 100;
    }

    console.log(`📈 Found ${aggregatedMetrics.routes.length} unique routes`);
    console.log(`⚡ Total requests: ${aggregatedMetrics.totalRequests}`);
    console.log(`⏱️  Average response time: ${aggregatedMetrics.averageResponseTime}ms`);
    console.log(`🐌 Slow routes: ${aggregatedMetrics.slowRoutes.length}`);

    // Generate reports
    const htmlFile = path.join(PERFORMANCE_DIR, `performance_report_${TIMESTAMP}.html`);
    const jsonFile = path.join(PERFORMANCE_DIR, `performance_report_${TIMESTAMP}.json`);

    generateHTMLReport(aggregatedMetrics, htmlFile);
    generateJSONReport(aggregatedMetrics, jsonFile);

    console.log('✅ Performance report generation completed!');
    console.log(`📊 Open ${htmlFile} to view the detailed report`);
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = {
    extractPerformanceMetrics,
    generateHTMLReport,
    generateJSONReport,
    generateRecommendations
};