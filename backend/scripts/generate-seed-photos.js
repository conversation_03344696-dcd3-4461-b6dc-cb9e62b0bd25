#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const axios = require('axios');

// Configuration
let PHOTOS_TO_GENERATE = 50; // Change this number to generate more/fewer photos
const PHOTO_WIDTH = 1080;
const PHOTO_HEIGHT = 1920;
const SEED_PHOTOS_DIR = path.join(__dirname, '..', 'assets', 'seed_photos');

/**
 * Ensures the seed_photos directory exists
 */
function ensureDirectoryExists() {
    if (!fs.existsSync(SEED_PHOTOS_DIR)) {
        fs.mkdirSync(SEED_PHOTOS_DIR, { recursive: true });
        console.log(`✅ Created directory: ${SEED_PHOTOS_DIR}`);
    }
}

/**
 * Gets existing photo numbers and finds missing ones
 */
function getPhotoStatus() {
    if (!fs.existsSync(SEED_PHOTOS_DIR)) {
        return { existing: [], missing: [], nextNumber: 1 };
    }

    const files = fs.readdirSync(SEED_PHOTOS_DIR);
    const existingNumbers = files
        .filter(file => file.match(/^\d+\.jpg$/))
        .map(file => parseInt(file.split('.')[0]))
        .sort((a, b) => a - b);

    if (existingNumbers.length === 0) {
        return { existing: [], missing: [], nextNumber: 1 };
    }

    // Find missing numbers in the sequence
    const missing = [];
    const maxNumber = Math.max(...existingNumbers);

    for (let i = 1; i <= maxNumber; i++) {
        if (!existingNumbers.includes(i)) {
            missing.push(i);
        }
    }

    const nextNumber = maxNumber + 1;

    return { existing: existingNumbers, missing, nextNumber };
}

/**
 * Downloads a photo from Picsum Photos API
 */
async function downloadPhoto(photoNumber) {
    const url = `https://picsum.photos/${PHOTO_WIDTH}/${PHOTO_HEIGHT}`;
    const filePath = path.join(SEED_PHOTOS_DIR, `${photoNumber}.jpg`);

    try {
        console.log(`📥 Downloading photo ${photoNumber}...`);

        const response = await axios({
            method: 'GET',
            url: url,
            responseType: 'stream',
            timeout: 30000, // 30 second timeout
        });

        const writer = fs.createWriteStream(filePath);
        response.data.pipe(writer);

        return new Promise((resolve, reject) => {
            writer.on('finish', () => {
                console.log(`✅ Downloaded: ${photoNumber}.jpg`);
                resolve();
            });
            writer.on('error', reject);
        });
    } catch (error) {
        console.error(`❌ Failed to download photo ${photoNumber}:`, error.message);
        throw error;
    }
}

/**
 * Main function to generate seed photos
 */
async function generateSeedPhotos() {
    console.log('🚀 Starting seed photo generation...');
    console.log(`📐 Photo dimensions: ${PHOTO_WIDTH}x${PHOTO_HEIGHT}`);
    console.log(`📁 Target directory: ${SEED_PHOTOS_DIR}`);

    ensureDirectoryExists();

    const { existing, missing, nextNumber } = getPhotoStatus();
    const existingCount = existing.length;

    console.log(`📊 Existing photos: ${existingCount}`);
    console.log(`🎯 Target total photos: ${PHOTOS_TO_GENERATE}`);

    if (missing.length > 0) {
        console.log(`🔍 Found ${missing.length} missing photos in sequence: ${missing.join(', ')}`);
    }

    if (existingCount >= PHOTOS_TO_GENERATE && missing.length === 0) {
        console.log(`✅ Already have ${existingCount} photos (target: ${PHOTOS_TO_GENERATE}). No new photos needed.`);
        return;
    }

    // Calculate what photos we need to download
    const photosToDownload = [];

    // First, fill in any gaps
    for (const missingNumber of missing) {
        if (photosToDownload.length + existingCount < PHOTOS_TO_GENERATE) {
            photosToDownload.push(missingNumber);
        }
    }

    // Then add new photos at the end if we still need more
    let currentNumber = nextNumber;
    while (photosToDownload.length + existingCount < PHOTOS_TO_GENERATE) {
        photosToDownload.push(currentNumber);
        currentNumber++;
    }

    console.log(`📥 Need to download: ${photosToDownload.length} photos`);
    if (photosToDownload.length > 0) {
        console.log(`🔢 Photo numbers to download: ${photosToDownload.join(', ')}`);
    }

    let successCount = 0;
    let failureCount = 0;

    for (let i = 0; i < photosToDownload.length; i++) {
        const photoNumber = photosToDownload[i];

        try {
            await downloadPhoto(photoNumber);
            successCount++;

            // Add a small delay between downloads to be respectful to the API
            if (i < photosToDownload.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        } catch (error) {
            failureCount++;
            console.error(`❌ Failed to download photo ${photoNumber}`);
        }
    }

    console.log('\n📊 Generation Summary:');
    console.log(`✅ Successfully downloaded: ${successCount} photos`);
    console.log(`❌ Failed downloads: ${failureCount} photos`);
    console.log(`📁 Total photos in directory: ${existingCount + successCount}`);

    if (failureCount > 0) {
        console.log('\n⚠️  Some downloads failed. You can run this script again to retry.');
        process.exit(1);
    } else {
        console.log('\n🎉 All photos generated successfully!');
    }
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    console.log(`
📸 Seed Photo Generator

Usage: node generate-seed-photos.js [options]

Options:
  --help, -h     Show this help message
  --count, -c    Number of photos to generate (default: ${PHOTOS_TO_GENERATE})

Examples:
  node generate-seed-photos.js
  node generate-seed-photos.js --count 100

Configuration:
  - Photo dimensions: ${PHOTO_WIDTH}x${PHOTO_HEIGHT} (mobile portrait)
  - Source: Picsum Photos (https://picsum.photos)
  - Output directory: backend/assets/seed_photos/
  - File naming: 1.jpg, 2.jpg, 3.jpg, etc.

The script will check existing photos and only download additional ones if needed.
    `);
    process.exit(0);
}

// Handle count argument
const countIndex = process.argv.findIndex(arg => arg === '--count' || arg === '-c');
if (countIndex !== -1 && process.argv[countIndex + 1]) {
    const customCount = parseInt(process.argv[countIndex + 1]);
    if (!isNaN(customCount) && customCount > 0) {
        PHOTOS_TO_GENERATE = customCount;
        console.log(`📊 Custom photo count: ${PHOTOS_TO_GENERATE}`);
    } else {
        console.error('❌ Invalid count value. Please provide a positive number.');
        process.exit(1);
    }
}

// Run the script
if (require.main === module) {
    generateSeedPhotos().catch(error => {
        console.error('💥 Script failed:', error.message);
        process.exit(1);
    });
}

module.exports = { generateSeedPhotos };
