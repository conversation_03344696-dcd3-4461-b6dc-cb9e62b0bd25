#!/usr/bin/env ts-node
"use strict";
/**
 * <PERSON><PERSON><PERSON> to import existing AWS resources into CDK stack
 * This helps resolve conflicts when resources already exist from failed deployments
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const AWS = __importStar(require("aws-sdk"));
class ResourceImporter {
    region;
    environment;
    projectName;
    dynamodb;
    s3;
    cognito;
    sns;
    lambda;
    constructor(region, environment, projectName) {
        this.region = region;
        this.environment = environment;
        this.projectName = projectName;
        AWS.config.update({ region: this.region });
        this.dynamodb = new AWS.DynamoDB();
        this.s3 = new AWS.S3();
        this.cognito = new AWS.CognitoIdentityServiceProvider();
        this.sns = new AWS.SNS();
        this.lambda = new AWS.Lambda();
    }
    async findExistingResources() {
        const resources = [];
        console.log('🔍 Scanning for existing resources...');
        // Check DynamoDB tables
        await this.findDynamoDBTables(resources);
        // Check S3 buckets
        await this.findS3Buckets(resources);
        // Check Cognito User Pools
        await this.findCognitoUserPools(resources);
        // Check SNS Topics
        await this.findSNSTopics(resources);
        // Check Lambda Functions
        await this.findLambdaFunctions(resources);
        return resources;
    }
    async findDynamoDBTables(resources) {
        const tableNames = [
            'Posts', 'Media', 'UserProfiles', 'Comments', 'Likes', 'Follows',
            'Channels', 'ChannelMembers', 'Reflexes', 'ReflexLikes', 'PostReactions',
            'Users', 'PostViews', 'ReflexReactions', 'UserPreferences', 'XboxAccounts',
            'PostEngagementMetrics', 'Notifications', 'DeviceTokens',
            'NotificationPreferences', 'NotificationHistory'
        ];
        for (const tableName of tableNames) {
            const fullTableName = `${this.projectName}-${this.environment}-${tableName}`;
            try {
                await this.dynamodb.describeTable({ TableName: fullTableName }).promise();
                resources.push({
                    resourceType: 'AWS::DynamoDB::Table',
                    logicalId: `${tableName}Table`,
                    physicalResourceId: fullTableName
                });
                console.log(`✅ Found DynamoDB table: ${fullTableName}`);
            }
            catch (error) {
                console.log(`❌ DynamoDB table not found: ${fullTableName}`);
            }
        }
    }
    async findS3Buckets(resources) {
        const bucketNames = [
            `${this.projectName}-media-${this.environment}`,
            `${this.projectName}-landing-page-${this.environment}`,
            `${this.projectName}-landing-page-logs-${this.environment}`
        ];
        for (const bucketName of bucketNames) {
            try {
                await this.s3.headBucket({ Bucket: bucketName }).promise();
                const logicalId = bucketName.includes('media') ? 'MediaBucket' :
                    bucketName.includes('logs') ? 'LandingPageLogsBucket' : 'LandingPageBucket';
                resources.push({
                    resourceType: 'AWS::S3::Bucket',
                    logicalId,
                    physicalResourceId: bucketName
                });
                console.log(`✅ Found S3 bucket: ${bucketName}`);
            }
            catch (error) {
                console.log(`❌ S3 bucket not found: ${bucketName}`);
            }
        }
    }
    async findCognitoUserPools(resources) {
        try {
            const userPools = await this.cognito.listUserPools({ MaxResults: 60 }).promise();
            const targetPoolName = `${this.projectName}-users-${this.environment}`;
            const pool = userPools.UserPools?.find(p => p.Name === targetPoolName);
            if (pool && pool.Id) {
                resources.push({
                    resourceType: 'AWS::Cognito::UserPool',
                    logicalId: 'UserPool',
                    physicalResourceId: pool.Id
                });
                console.log(`✅ Found Cognito User Pool: ${targetPoolName} (${pool.Id})`);
                // Also check for user pool client
                const clients = await this.cognito.listUserPoolClients({ UserPoolId: pool.Id }).promise();
                const targetClientName = `${this.projectName}-client-${this.environment}`;
                const client = clients.UserPoolClients?.find(c => c.ClientName === targetClientName);
                if (client && client.ClientId) {
                    resources.push({
                        resourceType: 'AWS::Cognito::UserPoolClient',
                        logicalId: 'UserPoolClient',
                        physicalResourceId: client.ClientId
                    });
                    console.log(`✅ Found Cognito User Pool Client: ${targetClientName} (${client.ClientId})`);
                }
            }
        }
        catch (error) {
            console.log(`❌ Error checking Cognito User Pools: ${error}`);
        }
    }
    async findSNSTopics(resources) {
        try {
            const topics = await this.sns.listTopics().promise();
            const targetTopicName = `${this.projectName}-notifications-${this.environment}`;
            const topic = topics.Topics?.find(t => t.TopicArn?.includes(targetTopicName));
            if (topic && topic.TopicArn) {
                resources.push({
                    resourceType: 'AWS::SNS::Topic',
                    logicalId: 'NotificationTopic',
                    physicalResourceId: topic.TopicArn
                });
                console.log(`✅ Found SNS Topic: ${targetTopicName}`);
            }
        }
        catch (error) {
            console.log(`❌ Error checking SNS Topics: ${error}`);
        }
    }
    async findLambdaFunctions(resources) {
        const functionNames = [
            `${this.projectName}-custom-message-${this.environment}`,
            `${this.projectName}-ai-processing-${this.environment}`,
            `${this.projectName}-notifications-${this.environment}`
        ];
        for (const functionName of functionNames) {
            try {
                await this.lambda.getFunction({ FunctionName: functionName }).promise();
                const logicalId = functionName.includes('custom-message') ? 'CustomMessageFunction' :
                    functionName.includes('ai-processing') ? 'AiProcessingFunction' : 'NotificationFunction';
                resources.push({
                    resourceType: 'AWS::Lambda::Function',
                    logicalId,
                    physicalResourceId: functionName
                });
                console.log(`✅ Found Lambda function: ${functionName}`);
            }
            catch (error) {
                console.log(`❌ Lambda function not found: ${functionName}`);
            }
        }
    }
    async generateImportFile(resources) {
        if (resources.length === 0) {
            console.log('🎉 No existing resources found that need importing');
            return;
        }
        const importData = {
            version: 1,
            resources: resources.map(r => ({
                resourceType: r.resourceType,
                logicalResourceId: r.logicalId,
                resourceIdentifier: {
                    [this.getResourceIdentifierKey(r.resourceType)]: r.physicalResourceId
                }
            }))
        };
        const importFile = `import-${this.environment}.json`;
        require('fs').writeFileSync(importFile, JSON.stringify(importData, null, 2));
        console.log(`📄 Generated import file: ${importFile}`);
        console.log(`📋 Found ${resources.length} resources to import`);
    }
    getResourceIdentifierKey(resourceType) {
        switch (resourceType) {
            case 'AWS::DynamoDB::Table': return 'TableName';
            case 'AWS::S3::Bucket': return 'BucketName';
            case 'AWS::Cognito::UserPool': return 'UserPoolId';
            case 'AWS::Cognito::UserPoolClient': return 'ClientId';
            case 'AWS::SNS::Topic': return 'TopicArn';
            case 'AWS::Lambda::Function': return 'FunctionName';
            default: return 'PhysicalResourceId';
        }
    }
}
async function main() {
    const environment = process.argv[2] || 'production';
    const region = environment === 'production' ? 'us-east-1' : 'us-west-2';
    const projectName = 'gameflex';
    console.log(`🚀 Starting resource import for ${environment} environment in ${region}`);
    const importer = new ResourceImporter(region, environment, projectName);
    const resources = await importer.findExistingResources();
    await importer.generateImportFile(resources);
    if (resources.length > 0) {
        console.log('\n📝 Next steps:');
        console.log('1. Review the generated import file');
        console.log(`2. Run: cdk import --resource-mapping import-${environment}.json ${projectName}-${environment}`);
    }
}
if (require.main === module) {
    main().catch(console.error);
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW1wb3J0LWV4aXN0aW5nLXJlc291cmNlcy5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbImltcG9ydC1leGlzdGluZy1yZXNvdXJjZXMudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7QUFFQTs7O0dBR0c7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFSCw2Q0FBK0I7QUFTL0IsTUFBTSxnQkFBZ0I7SUFPQTtJQUF3QjtJQUE2QjtJQU5qRSxRQUFRLENBQWU7SUFDdkIsRUFBRSxDQUFTO0lBQ1gsT0FBTyxDQUFxQztJQUM1QyxHQUFHLENBQVU7SUFDYixNQUFNLENBQWE7SUFFM0IsWUFBb0IsTUFBYyxFQUFVLFdBQW1CLEVBQVUsV0FBbUI7UUFBeEUsV0FBTSxHQUFOLE1BQU0sQ0FBUTtRQUFVLGdCQUFXLEdBQVgsV0FBVyxDQUFRO1FBQVUsZ0JBQVcsR0FBWCxXQUFXLENBQVE7UUFDMUYsR0FBRyxDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsRUFBRSxNQUFNLEVBQUUsSUFBSSxDQUFDLE1BQU0sRUFBRSxDQUFDLENBQUM7UUFDM0MsSUFBSSxDQUFDLFFBQVEsR0FBRyxJQUFJLEdBQUcsQ0FBQyxRQUFRLEVBQUUsQ0FBQztRQUNuQyxJQUFJLENBQUMsRUFBRSxHQUFHLElBQUksR0FBRyxDQUFDLEVBQUUsRUFBRSxDQUFDO1FBQ3ZCLElBQUksQ0FBQyxPQUFPLEdBQUcsSUFBSSxHQUFHLENBQUMsOEJBQThCLEVBQUUsQ0FBQztRQUN4RCxJQUFJLENBQUMsR0FBRyxHQUFHLElBQUksR0FBRyxDQUFDLEdBQUcsRUFBRSxDQUFDO1FBQ3pCLElBQUksQ0FBQyxNQUFNLEdBQUcsSUFBSSxHQUFHLENBQUMsTUFBTSxFQUFFLENBQUM7SUFDakMsQ0FBQztJQUVELEtBQUssQ0FBQyxxQkFBcUI7UUFDekIsTUFBTSxTQUFTLEdBQXVCLEVBQUUsQ0FBQztRQUV6QyxPQUFPLENBQUMsR0FBRyxDQUFDLHVDQUF1QyxDQUFDLENBQUM7UUFFckQsd0JBQXdCO1FBQ3hCLE1BQU0sSUFBSSxDQUFDLGtCQUFrQixDQUFDLFNBQVMsQ0FBQyxDQUFDO1FBRXpDLG1CQUFtQjtRQUNuQixNQUFNLElBQUksQ0FBQyxhQUFhLENBQUMsU0FBUyxDQUFDLENBQUM7UUFFcEMsMkJBQTJCO1FBQzNCLE1BQU0sSUFBSSxDQUFDLG9CQUFvQixDQUFDLFNBQVMsQ0FBQyxDQUFDO1FBRTNDLG1CQUFtQjtRQUNuQixNQUFNLElBQUksQ0FBQyxhQUFhLENBQUMsU0FBUyxDQUFDLENBQUM7UUFFcEMseUJBQXlCO1FBQ3pCLE1BQU0sSUFBSSxDQUFDLG1CQUFtQixDQUFDLFNBQVMsQ0FBQyxDQUFDO1FBRTFDLE9BQU8sU0FBUyxDQUFDO0lBQ25CLENBQUM7SUFFTyxLQUFLLENBQUMsa0JBQWtCLENBQUMsU0FBNkI7UUFDNUQsTUFBTSxVQUFVLEdBQUc7WUFDakIsT0FBTyxFQUFFLE9BQU8sRUFBRSxjQUFjLEVBQUUsVUFBVSxFQUFFLE9BQU8sRUFBRSxTQUFTO1lBQ2hFLFVBQVUsRUFBRSxnQkFBZ0IsRUFBRSxVQUFVLEVBQUUsYUFBYSxFQUFFLGVBQWU7WUFDeEUsT0FBTyxFQUFFLFdBQVcsRUFBRSxpQkFBaUIsRUFBRSxpQkFBaUIsRUFBRSxjQUFjO1lBQzFFLHVCQUF1QixFQUFFLGVBQWUsRUFBRSxjQUFjO1lBQ3hELHlCQUF5QixFQUFFLHFCQUFxQjtTQUNqRCxDQUFDO1FBRUYsS0FBSyxNQUFNLFNBQVMsSUFBSSxVQUFVLEVBQUUsQ0FBQztZQUNuQyxNQUFNLGFBQWEsR0FBRyxHQUFHLElBQUksQ0FBQyxXQUFXLElBQUksSUFBSSxDQUFDLFdBQVcsSUFBSSxTQUFTLEVBQUUsQ0FBQztZQUM3RSxJQUFJLENBQUM7Z0JBQ0gsTUFBTSxJQUFJLENBQUMsUUFBUSxDQUFDLGFBQWEsQ0FBQyxFQUFFLFNBQVMsRUFBRSxhQUFhLEVBQUUsQ0FBQyxDQUFDLE9BQU8sRUFBRSxDQUFDO2dCQUMxRSxTQUFTLENBQUMsSUFBSSxDQUFDO29CQUNiLFlBQVksRUFBRSxzQkFBc0I7b0JBQ3BDLFNBQVMsRUFBRSxHQUFHLFNBQVMsT0FBTztvQkFDOUIsa0JBQWtCLEVBQUUsYUFBYTtpQkFDbEMsQ0FBQyxDQUFDO2dCQUNILE9BQU8sQ0FBQyxHQUFHLENBQUMsMkJBQTJCLGFBQWEsRUFBRSxDQUFDLENBQUM7WUFDMUQsQ0FBQztZQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7Z0JBQ2YsT0FBTyxDQUFDLEdBQUcsQ0FBQywrQkFBK0IsYUFBYSxFQUFFLENBQUMsQ0FBQztZQUM5RCxDQUFDO1FBQ0gsQ0FBQztJQUNILENBQUM7SUFFTyxLQUFLLENBQUMsYUFBYSxDQUFDLFNBQTZCO1FBQ3ZELE1BQU0sV0FBVyxHQUFHO1lBQ2xCLEdBQUcsSUFBSSxDQUFDLFdBQVcsVUFBVSxJQUFJLENBQUMsV0FBVyxFQUFFO1lBQy9DLEdBQUcsSUFBSSxDQUFDLFdBQVcsaUJBQWlCLElBQUksQ0FBQyxXQUFXLEVBQUU7WUFDdEQsR0FBRyxJQUFJLENBQUMsV0FBVyxzQkFBc0IsSUFBSSxDQUFDLFdBQVcsRUFBRTtTQUM1RCxDQUFDO1FBRUYsS0FBSyxNQUFNLFVBQVUsSUFBSSxXQUFXLEVBQUUsQ0FBQztZQUNyQyxJQUFJLENBQUM7Z0JBQ0gsTUFBTSxJQUFJLENBQUMsRUFBRSxDQUFDLFVBQVUsQ0FBQyxFQUFFLE1BQU0sRUFBRSxVQUFVLEVBQUUsQ0FBQyxDQUFDLE9BQU8sRUFBRSxDQUFDO2dCQUMzRCxNQUFNLFNBQVMsR0FBRyxVQUFVLENBQUMsUUFBUSxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsQ0FBQyxhQUFhLENBQUMsQ0FBQztvQkFDL0MsVUFBVSxDQUFDLFFBQVEsQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsdUJBQXVCLENBQUMsQ0FBQyxDQUFDLG1CQUFtQixDQUFDO2dCQUM3RixTQUFTLENBQUMsSUFBSSxDQUFDO29CQUNiLFlBQVksRUFBRSxpQkFBaUI7b0JBQy9CLFNBQVM7b0JBQ1Qsa0JBQWtCLEVBQUUsVUFBVTtpQkFDL0IsQ0FBQyxDQUFDO2dCQUNILE9BQU8sQ0FBQyxHQUFHLENBQUMsc0JBQXNCLFVBQVUsRUFBRSxDQUFDLENBQUM7WUFDbEQsQ0FBQztZQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7Z0JBQ2YsT0FBTyxDQUFDLEdBQUcsQ0FBQywwQkFBMEIsVUFBVSxFQUFFLENBQUMsQ0FBQztZQUN0RCxDQUFDO1FBQ0gsQ0FBQztJQUNILENBQUM7SUFFTyxLQUFLLENBQUMsb0JBQW9CLENBQUMsU0FBNkI7UUFDOUQsSUFBSSxDQUFDO1lBQ0gsTUFBTSxTQUFTLEdBQUcsTUFBTSxJQUFJLENBQUMsT0FBTyxDQUFDLGFBQWEsQ0FBQyxFQUFFLFVBQVUsRUFBRSxFQUFFLEVBQUUsQ0FBQyxDQUFDLE9BQU8sRUFBRSxDQUFDO1lBQ2pGLE1BQU0sY0FBYyxHQUFHLEdBQUcsSUFBSSxDQUFDLFdBQVcsVUFBVSxJQUFJLENBQUMsV0FBVyxFQUFFLENBQUM7WUFFdkUsTUFBTSxJQUFJLEdBQUcsU0FBUyxDQUFDLFNBQVMsRUFBRSxJQUFJLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsSUFBSSxLQUFLLGNBQWMsQ0FBQyxDQUFDO1lBQ3ZFLElBQUksSUFBSSxJQUFJLElBQUksQ0FBQyxFQUFFLEVBQUUsQ0FBQztnQkFDcEIsU0FBUyxDQUFDLElBQUksQ0FBQztvQkFDYixZQUFZLEVBQUUsd0JBQXdCO29CQUN0QyxTQUFTLEVBQUUsVUFBVTtvQkFDckIsa0JBQWtCLEVBQUUsSUFBSSxDQUFDLEVBQUU7aUJBQzVCLENBQUMsQ0FBQztnQkFDSCxPQUFPLENBQUMsR0FBRyxDQUFDLDhCQUE4QixjQUFjLEtBQUssSUFBSSxDQUFDLEVBQUUsR0FBRyxDQUFDLENBQUM7Z0JBRXpFLGtDQUFrQztnQkFDbEMsTUFBTSxPQUFPLEdBQUcsTUFBTSxJQUFJLENBQUMsT0FBTyxDQUFDLG1CQUFtQixDQUFDLEVBQUUsVUFBVSxFQUFFLElBQUksQ0FBQyxFQUFFLEVBQUUsQ0FBQyxDQUFDLE9BQU8sRUFBRSxDQUFDO2dCQUMxRixNQUFNLGdCQUFnQixHQUFHLEdBQUcsSUFBSSxDQUFDLFdBQVcsV0FBVyxJQUFJLENBQUMsV0FBVyxFQUFFLENBQUM7Z0JBQzFFLE1BQU0sTUFBTSxHQUFHLE9BQU8sQ0FBQyxlQUFlLEVBQUUsSUFBSSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLFVBQVUsS0FBSyxnQkFBZ0IsQ0FBQyxDQUFDO2dCQUNyRixJQUFJLE1BQU0sSUFBSSxNQUFNLENBQUMsUUFBUSxFQUFFLENBQUM7b0JBQzlCLFNBQVMsQ0FBQyxJQUFJLENBQUM7d0JBQ2IsWUFBWSxFQUFFLDhCQUE4Qjt3QkFDNUMsU0FBUyxFQUFFLGdCQUFnQjt3QkFDM0Isa0JBQWtCLEVBQUUsTUFBTSxDQUFDLFFBQVE7cUJBQ3BDLENBQUMsQ0FBQztvQkFDSCxPQUFPLENBQUMsR0FBRyxDQUFDLHFDQUFxQyxnQkFBZ0IsS0FBSyxNQUFNLENBQUMsUUFBUSxHQUFHLENBQUMsQ0FBQztnQkFDNUYsQ0FBQztZQUNILENBQUM7UUFDSCxDQUFDO1FBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztZQUNmLE9BQU8sQ0FBQyxHQUFHLENBQUMsd0NBQXdDLEtBQUssRUFBRSxDQUFDLENBQUM7UUFDL0QsQ0FBQztJQUNILENBQUM7SUFFTyxLQUFLLENBQUMsYUFBYSxDQUFDLFNBQTZCO1FBQ3ZELElBQUksQ0FBQztZQUNILE1BQU0sTUFBTSxHQUFHLE1BQU0sSUFBSSxDQUFDLEdBQUcsQ0FBQyxVQUFVLEVBQUUsQ0FBQyxPQUFPLEVBQUUsQ0FBQztZQUNyRCxNQUFNLGVBQWUsR0FBRyxHQUFHLElBQUksQ0FBQyxXQUFXLGtCQUFrQixJQUFJLENBQUMsV0FBVyxFQUFFLENBQUM7WUFFaEYsTUFBTSxLQUFLLEdBQUcsTUFBTSxDQUFDLE1BQU0sRUFBRSxJQUFJLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsUUFBUSxFQUFFLFFBQVEsQ0FBQyxlQUFlLENBQUMsQ0FBQyxDQUFDO1lBQzlFLElBQUksS0FBSyxJQUFJLEtBQUssQ0FBQyxRQUFRLEVBQUUsQ0FBQztnQkFDNUIsU0FBUyxDQUFDLElBQUksQ0FBQztvQkFDYixZQUFZLEVBQUUsaUJBQWlCO29CQUMvQixTQUFTLEVBQUUsbUJBQW1CO29CQUM5QixrQkFBa0IsRUFBRSxLQUFLLENBQUMsUUFBUTtpQkFDbkMsQ0FBQyxDQUFDO2dCQUNILE9BQU8sQ0FBQyxHQUFHLENBQUMsc0JBQXNCLGVBQWUsRUFBRSxDQUFDLENBQUM7WUFDdkQsQ0FBQztRQUNILENBQUM7UUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO1lBQ2YsT0FBTyxDQUFDLEdBQUcsQ0FBQyxnQ0FBZ0MsS0FBSyxFQUFFLENBQUMsQ0FBQztRQUN2RCxDQUFDO0lBQ0gsQ0FBQztJQUVPLEtBQUssQ0FBQyxtQkFBbUIsQ0FBQyxTQUE2QjtRQUM3RCxNQUFNLGFBQWEsR0FBRztZQUNwQixHQUFHLElBQUksQ0FBQyxXQUFXLG1CQUFtQixJQUFJLENBQUMsV0FBVyxFQUFFO1lBQ3hELEdBQUcsSUFBSSxDQUFDLFdBQVcsa0JBQWtCLElBQUksQ0FBQyxXQUFXLEVBQUU7WUFDdkQsR0FBRyxJQUFJLENBQUMsV0FBVyxrQkFBa0IsSUFBSSxDQUFDLFdBQVcsRUFBRTtTQUN4RCxDQUFDO1FBRUYsS0FBSyxNQUFNLFlBQVksSUFBSSxhQUFhLEVBQUUsQ0FBQztZQUN6QyxJQUFJLENBQUM7Z0JBQ0gsTUFBTSxJQUFJLENBQUMsTUFBTSxDQUFDLFdBQVcsQ0FBQyxFQUFFLFlBQVksRUFBRSxZQUFZLEVBQUUsQ0FBQyxDQUFDLE9BQU8sRUFBRSxDQUFDO2dCQUN4RSxNQUFNLFNBQVMsR0FBRyxZQUFZLENBQUMsUUFBUSxDQUFDLGdCQUFnQixDQUFDLENBQUMsQ0FBQyxDQUFDLHVCQUF1QixDQUFDLENBQUM7b0JBQ3BFLFlBQVksQ0FBQyxRQUFRLENBQUMsZUFBZSxDQUFDLENBQUMsQ0FBQyxDQUFDLHNCQUFzQixDQUFDLENBQUMsQ0FBQyxzQkFBc0IsQ0FBQztnQkFDMUcsU0FBUyxDQUFDLElBQUksQ0FBQztvQkFDYixZQUFZLEVBQUUsdUJBQXVCO29CQUNyQyxTQUFTO29CQUNULGtCQUFrQixFQUFFLFlBQVk7aUJBQ2pDLENBQUMsQ0FBQztnQkFDSCxPQUFPLENBQUMsR0FBRyxDQUFDLDRCQUE0QixZQUFZLEVBQUUsQ0FBQyxDQUFDO1lBQzFELENBQUM7WUFBQyxPQUFPLEtBQUssRUFBRSxDQUFDO2dCQUNmLE9BQU8sQ0FBQyxHQUFHLENBQUMsZ0NBQWdDLFlBQVksRUFBRSxDQUFDLENBQUM7WUFDOUQsQ0FBQztRQUNILENBQUM7SUFDSCxDQUFDO0lBRUQsS0FBSyxDQUFDLGtCQUFrQixDQUFDLFNBQTZCO1FBQ3BELElBQUksU0FBUyxDQUFDLE1BQU0sS0FBSyxDQUFDLEVBQUUsQ0FBQztZQUMzQixPQUFPLENBQUMsR0FBRyxDQUFDLG9EQUFvRCxDQUFDLENBQUM7WUFDbEUsT0FBTztRQUNULENBQUM7UUFFRCxNQUFNLFVBQVUsR0FBRztZQUNqQixPQUFPLEVBQUUsQ0FBQztZQUNWLFNBQVMsRUFBRSxTQUFTLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQztnQkFDN0IsWUFBWSxFQUFFLENBQUMsQ0FBQyxZQUFZO2dCQUM1QixpQkFBaUIsRUFBRSxDQUFDLENBQUMsU0FBUztnQkFDOUIsa0JBQWtCLEVBQUU7b0JBQ2xCLENBQUMsSUFBSSxDQUFDLHdCQUF3QixDQUFDLENBQUMsQ0FBQyxZQUFZLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxrQkFBa0I7aUJBQ3RFO2FBQ0YsQ0FBQyxDQUFDO1NBQ0osQ0FBQztRQUVGLE1BQU0sVUFBVSxHQUFHLFVBQVUsSUFBSSxDQUFDLFdBQVcsT0FBTyxDQUFDO1FBQ3JELE9BQU8sQ0FBQyxJQUFJLENBQUMsQ0FBQyxhQUFhLENBQUMsVUFBVSxFQUFFLElBQUksQ0FBQyxTQUFTLENBQUMsVUFBVSxFQUFFLElBQUksRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQzdFLE9BQU8sQ0FBQyxHQUFHLENBQUMsNkJBQTZCLFVBQVUsRUFBRSxDQUFDLENBQUM7UUFDdkQsT0FBTyxDQUFDLEdBQUcsQ0FBQyxZQUFZLFNBQVMsQ0FBQyxNQUFNLHNCQUFzQixDQUFDLENBQUM7SUFDbEUsQ0FBQztJQUVPLHdCQUF3QixDQUFDLFlBQW9CO1FBQ25ELFFBQVEsWUFBWSxFQUFFLENBQUM7WUFDckIsS0FBSyxzQkFBc0IsQ0FBQyxDQUFDLE9BQU8sV0FBVyxDQUFDO1lBQ2hELEtBQUssaUJBQWlCLENBQUMsQ0FBQyxPQUFPLFlBQVksQ0FBQztZQUM1QyxLQUFLLHdCQUF3QixDQUFDLENBQUMsT0FBTyxZQUFZLENBQUM7WUFDbkQsS0FBSyw4QkFBOEIsQ0FBQyxDQUFDLE9BQU8sVUFBVSxDQUFDO1lBQ3ZELEtBQUssaUJBQWlCLENBQUMsQ0FBQyxPQUFPLFVBQVUsQ0FBQztZQUMxQyxLQUFLLHVCQUF1QixDQUFDLENBQUMsT0FBTyxjQUFjLENBQUM7WUFDcEQsT0FBTyxDQUFDLENBQUMsT0FBTyxvQkFBb0IsQ0FBQztRQUN2QyxDQUFDO0lBQ0gsQ0FBQztDQUNGO0FBRUQsS0FBSyxVQUFVLElBQUk7SUFDakIsTUFBTSxXQUFXLEdBQUcsT0FBTyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsSUFBSSxZQUFZLENBQUM7SUFDcEQsTUFBTSxNQUFNLEdBQUcsV0FBVyxLQUFLLFlBQVksQ0FBQyxDQUFDLENBQUMsV0FBVyxDQUFDLENBQUMsQ0FBQyxXQUFXLENBQUM7SUFDeEUsTUFBTSxXQUFXLEdBQUcsVUFBVSxDQUFDO0lBRS9CLE9BQU8sQ0FBQyxHQUFHLENBQUMsbUNBQW1DLFdBQVcsbUJBQW1CLE1BQU0sRUFBRSxDQUFDLENBQUM7SUFFdkYsTUFBTSxRQUFRLEdBQUcsSUFBSSxnQkFBZ0IsQ0FBQyxNQUFNLEVBQUUsV0FBVyxFQUFFLFdBQVcsQ0FBQyxDQUFDO0lBQ3hFLE1BQU0sU0FBUyxHQUFHLE1BQU0sUUFBUSxDQUFDLHFCQUFxQixFQUFFLENBQUM7SUFDekQsTUFBTSxRQUFRLENBQUMsa0JBQWtCLENBQUMsU0FBUyxDQUFDLENBQUM7SUFFN0MsSUFBSSxTQUFTLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRSxDQUFDO1FBQ3pCLE9BQU8sQ0FBQyxHQUFHLENBQUMsa0JBQWtCLENBQUMsQ0FBQztRQUNoQyxPQUFPLENBQUMsR0FBRyxDQUFDLHFDQUFxQyxDQUFDLENBQUM7UUFDbkQsT0FBTyxDQUFDLEdBQUcsQ0FBQyxnREFBZ0QsV0FBVyxTQUFTLFdBQVcsSUFBSSxXQUFXLEVBQUUsQ0FBQyxDQUFDO0lBQ2hILENBQUM7QUFDSCxDQUFDO0FBRUQsSUFBSSxPQUFPLENBQUMsSUFBSSxLQUFLLE1BQU0sRUFBRSxDQUFDO0lBQzVCLElBQUksRUFBRSxDQUFDLEtBQUssQ0FBQyxPQUFPLENBQUMsS0FBSyxDQUFDLENBQUM7QUFDOUIsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIiMhL3Vzci9iaW4vZW52IHRzLW5vZGVcblxuLyoqXG4gKiBTY3JpcHQgdG8gaW1wb3J0IGV4aXN0aW5nIEFXUyByZXNvdXJjZXMgaW50byBDREsgc3RhY2tcbiAqIFRoaXMgaGVscHMgcmVzb2x2ZSBjb25mbGljdHMgd2hlbiByZXNvdXJjZXMgYWxyZWFkeSBleGlzdCBmcm9tIGZhaWxlZCBkZXBsb3ltZW50c1xuICovXG5cbmltcG9ydCAqIGFzIEFXUyBmcm9tICdhd3Mtc2RrJztcbmltcG9ydCB7IGV4ZWNTeW5jIH0gZnJvbSAnY2hpbGRfcHJvY2Vzcyc7XG5cbmludGVyZmFjZSBSZXNvdXJjZVRvSW1wb3J0IHtcbiAgcmVzb3VyY2VUeXBlOiBzdHJpbmc7XG4gIGxvZ2ljYWxJZDogc3RyaW5nO1xuICBwaHlzaWNhbFJlc291cmNlSWQ6IHN0cmluZztcbn1cblxuY2xhc3MgUmVzb3VyY2VJbXBvcnRlciB7XG4gIHByaXZhdGUgZHluYW1vZGI6IEFXUy5EeW5hbW9EQjtcbiAgcHJpdmF0ZSBzMzogQVdTLlMzO1xuICBwcml2YXRlIGNvZ25pdG86IEFXUy5Db2duaXRvSWRlbnRpdHlTZXJ2aWNlUHJvdmlkZXI7XG4gIHByaXZhdGUgc25zOiBBV1MuU05TO1xuICBwcml2YXRlIGxhbWJkYTogQVdTLkxhbWJkYTtcbiAgXG4gIGNvbnN0cnVjdG9yKHByaXZhdGUgcmVnaW9uOiBzdHJpbmcsIHByaXZhdGUgZW52aXJvbm1lbnQ6IHN0cmluZywgcHJpdmF0ZSBwcm9qZWN0TmFtZTogc3RyaW5nKSB7XG4gICAgQVdTLmNvbmZpZy51cGRhdGUoeyByZWdpb246IHRoaXMucmVnaW9uIH0pO1xuICAgIHRoaXMuZHluYW1vZGIgPSBuZXcgQVdTLkR5bmFtb0RCKCk7XG4gICAgdGhpcy5zMyA9IG5ldyBBV1MuUzMoKTtcbiAgICB0aGlzLmNvZ25pdG8gPSBuZXcgQVdTLkNvZ25pdG9JZGVudGl0eVNlcnZpY2VQcm92aWRlcigpO1xuICAgIHRoaXMuc25zID0gbmV3IEFXUy5TTlMoKTtcbiAgICB0aGlzLmxhbWJkYSA9IG5ldyBBV1MuTGFtYmRhKCk7XG4gIH1cblxuICBhc3luYyBmaW5kRXhpc3RpbmdSZXNvdXJjZXMoKTogUHJvbWlzZTxSZXNvdXJjZVRvSW1wb3J0W10+IHtcbiAgICBjb25zdCByZXNvdXJjZXM6IFJlc291cmNlVG9JbXBvcnRbXSA9IFtdO1xuICAgIFxuICAgIGNvbnNvbGUubG9nKCfwn5SNIFNjYW5uaW5nIGZvciBleGlzdGluZyByZXNvdXJjZXMuLi4nKTtcbiAgICBcbiAgICAvLyBDaGVjayBEeW5hbW9EQiB0YWJsZXNcbiAgICBhd2FpdCB0aGlzLmZpbmREeW5hbW9EQlRhYmxlcyhyZXNvdXJjZXMpO1xuICAgIFxuICAgIC8vIENoZWNrIFMzIGJ1Y2tldHNcbiAgICBhd2FpdCB0aGlzLmZpbmRTM0J1Y2tldHMocmVzb3VyY2VzKTtcbiAgICBcbiAgICAvLyBDaGVjayBDb2duaXRvIFVzZXIgUG9vbHNcbiAgICBhd2FpdCB0aGlzLmZpbmRDb2duaXRvVXNlclBvb2xzKHJlc291cmNlcyk7XG4gICAgXG4gICAgLy8gQ2hlY2sgU05TIFRvcGljc1xuICAgIGF3YWl0IHRoaXMuZmluZFNOU1RvcGljcyhyZXNvdXJjZXMpO1xuICAgIFxuICAgIC8vIENoZWNrIExhbWJkYSBGdW5jdGlvbnNcbiAgICBhd2FpdCB0aGlzLmZpbmRMYW1iZGFGdW5jdGlvbnMocmVzb3VyY2VzKTtcbiAgICBcbiAgICByZXR1cm4gcmVzb3VyY2VzO1xuICB9XG5cbiAgcHJpdmF0ZSBhc3luYyBmaW5kRHluYW1vREJUYWJsZXMocmVzb3VyY2VzOiBSZXNvdXJjZVRvSW1wb3J0W10pOiBQcm9taXNlPHZvaWQ+IHtcbiAgICBjb25zdCB0YWJsZU5hbWVzID0gW1xuICAgICAgJ1Bvc3RzJywgJ01lZGlhJywgJ1VzZXJQcm9maWxlcycsICdDb21tZW50cycsICdMaWtlcycsICdGb2xsb3dzJywgXG4gICAgICAnQ2hhbm5lbHMnLCAnQ2hhbm5lbE1lbWJlcnMnLCAnUmVmbGV4ZXMnLCAnUmVmbGV4TGlrZXMnLCAnUG9zdFJlYWN0aW9ucycsXG4gICAgICAnVXNlcnMnLCAnUG9zdFZpZXdzJywgJ1JlZmxleFJlYWN0aW9ucycsICdVc2VyUHJlZmVyZW5jZXMnLCAnWGJveEFjY291bnRzJyxcbiAgICAgICdQb3N0RW5nYWdlbWVudE1ldHJpY3MnLCAnTm90aWZpY2F0aW9ucycsICdEZXZpY2VUb2tlbnMnLCBcbiAgICAgICdOb3RpZmljYXRpb25QcmVmZXJlbmNlcycsICdOb3RpZmljYXRpb25IaXN0b3J5J1xuICAgIF07XG5cbiAgICBmb3IgKGNvbnN0IHRhYmxlTmFtZSBvZiB0YWJsZU5hbWVzKSB7XG4gICAgICBjb25zdCBmdWxsVGFibGVOYW1lID0gYCR7dGhpcy5wcm9qZWN0TmFtZX0tJHt0aGlzLmVudmlyb25tZW50fS0ke3RhYmxlTmFtZX1gO1xuICAgICAgdHJ5IHtcbiAgICAgICAgYXdhaXQgdGhpcy5keW5hbW9kYi5kZXNjcmliZVRhYmxlKHsgVGFibGVOYW1lOiBmdWxsVGFibGVOYW1lIH0pLnByb21pc2UoKTtcbiAgICAgICAgcmVzb3VyY2VzLnB1c2goe1xuICAgICAgICAgIHJlc291cmNlVHlwZTogJ0FXUzo6RHluYW1vREI6OlRhYmxlJyxcbiAgICAgICAgICBsb2dpY2FsSWQ6IGAke3RhYmxlTmFtZX1UYWJsZWAsXG4gICAgICAgICAgcGh5c2ljYWxSZXNvdXJjZUlkOiBmdWxsVGFibGVOYW1lXG4gICAgICAgIH0pO1xuICAgICAgICBjb25zb2xlLmxvZyhg4pyFIEZvdW5kIER5bmFtb0RCIHRhYmxlOiAke2Z1bGxUYWJsZU5hbWV9YCk7XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmxvZyhg4p2MIER5bmFtb0RCIHRhYmxlIG5vdCBmb3VuZDogJHtmdWxsVGFibGVOYW1lfWApO1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIHByaXZhdGUgYXN5bmMgZmluZFMzQnVja2V0cyhyZXNvdXJjZXM6IFJlc291cmNlVG9JbXBvcnRbXSk6IFByb21pc2U8dm9pZD4ge1xuICAgIGNvbnN0IGJ1Y2tldE5hbWVzID0gW1xuICAgICAgYCR7dGhpcy5wcm9qZWN0TmFtZX0tbWVkaWEtJHt0aGlzLmVudmlyb25tZW50fWAsXG4gICAgICBgJHt0aGlzLnByb2plY3ROYW1lfS1sYW5kaW5nLXBhZ2UtJHt0aGlzLmVudmlyb25tZW50fWAsXG4gICAgICBgJHt0aGlzLnByb2plY3ROYW1lfS1sYW5kaW5nLXBhZ2UtbG9ncy0ke3RoaXMuZW52aXJvbm1lbnR9YFxuICAgIF07XG5cbiAgICBmb3IgKGNvbnN0IGJ1Y2tldE5hbWUgb2YgYnVja2V0TmFtZXMpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGF3YWl0IHRoaXMuczMuaGVhZEJ1Y2tldCh7IEJ1Y2tldDogYnVja2V0TmFtZSB9KS5wcm9taXNlKCk7XG4gICAgICAgIGNvbnN0IGxvZ2ljYWxJZCA9IGJ1Y2tldE5hbWUuaW5jbHVkZXMoJ21lZGlhJykgPyAnTWVkaWFCdWNrZXQnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICBidWNrZXROYW1lLmluY2x1ZGVzKCdsb2dzJykgPyAnTGFuZGluZ1BhZ2VMb2dzQnVja2V0JyA6ICdMYW5kaW5nUGFnZUJ1Y2tldCc7XG4gICAgICAgIHJlc291cmNlcy5wdXNoKHtcbiAgICAgICAgICByZXNvdXJjZVR5cGU6ICdBV1M6OlMzOjpCdWNrZXQnLFxuICAgICAgICAgIGxvZ2ljYWxJZCxcbiAgICAgICAgICBwaHlzaWNhbFJlc291cmNlSWQ6IGJ1Y2tldE5hbWVcbiAgICAgICAgfSk7XG4gICAgICAgIGNvbnNvbGUubG9nKGDinIUgRm91bmQgUzMgYnVja2V0OiAke2J1Y2tldE5hbWV9YCk7XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmxvZyhg4p2MIFMzIGJ1Y2tldCBub3QgZm91bmQ6ICR7YnVja2V0TmFtZX1gKTtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICBwcml2YXRlIGFzeW5jIGZpbmRDb2duaXRvVXNlclBvb2xzKHJlc291cmNlczogUmVzb3VyY2VUb0ltcG9ydFtdKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVzZXJQb29scyA9IGF3YWl0IHRoaXMuY29nbml0by5saXN0VXNlclBvb2xzKHsgTWF4UmVzdWx0czogNjAgfSkucHJvbWlzZSgpO1xuICAgICAgY29uc3QgdGFyZ2V0UG9vbE5hbWUgPSBgJHt0aGlzLnByb2plY3ROYW1lfS11c2Vycy0ke3RoaXMuZW52aXJvbm1lbnR9YDtcbiAgICAgIFxuICAgICAgY29uc3QgcG9vbCA9IHVzZXJQb29scy5Vc2VyUG9vbHM/LmZpbmQocCA9PiBwLk5hbWUgPT09IHRhcmdldFBvb2xOYW1lKTtcbiAgICAgIGlmIChwb29sICYmIHBvb2wuSWQpIHtcbiAgICAgICAgcmVzb3VyY2VzLnB1c2goe1xuICAgICAgICAgIHJlc291cmNlVHlwZTogJ0FXUzo6Q29nbml0bzo6VXNlclBvb2wnLFxuICAgICAgICAgIGxvZ2ljYWxJZDogJ1VzZXJQb29sJyxcbiAgICAgICAgICBwaHlzaWNhbFJlc291cmNlSWQ6IHBvb2wuSWRcbiAgICAgICAgfSk7XG4gICAgICAgIGNvbnNvbGUubG9nKGDinIUgRm91bmQgQ29nbml0byBVc2VyIFBvb2w6ICR7dGFyZ2V0UG9vbE5hbWV9ICgke3Bvb2wuSWR9KWApO1xuICAgICAgICBcbiAgICAgICAgLy8gQWxzbyBjaGVjayBmb3IgdXNlciBwb29sIGNsaWVudFxuICAgICAgICBjb25zdCBjbGllbnRzID0gYXdhaXQgdGhpcy5jb2duaXRvLmxpc3RVc2VyUG9vbENsaWVudHMoeyBVc2VyUG9vbElkOiBwb29sLklkIH0pLnByb21pc2UoKTtcbiAgICAgICAgY29uc3QgdGFyZ2V0Q2xpZW50TmFtZSA9IGAke3RoaXMucHJvamVjdE5hbWV9LWNsaWVudC0ke3RoaXMuZW52aXJvbm1lbnR9YDtcbiAgICAgICAgY29uc3QgY2xpZW50ID0gY2xpZW50cy5Vc2VyUG9vbENsaWVudHM/LmZpbmQoYyA9PiBjLkNsaWVudE5hbWUgPT09IHRhcmdldENsaWVudE5hbWUpO1xuICAgICAgICBpZiAoY2xpZW50ICYmIGNsaWVudC5DbGllbnRJZCkge1xuICAgICAgICAgIHJlc291cmNlcy5wdXNoKHtcbiAgICAgICAgICAgIHJlc291cmNlVHlwZTogJ0FXUzo6Q29nbml0bzo6VXNlclBvb2xDbGllbnQnLFxuICAgICAgICAgICAgbG9naWNhbElkOiAnVXNlclBvb2xDbGllbnQnLFxuICAgICAgICAgICAgcGh5c2ljYWxSZXNvdXJjZUlkOiBjbGllbnQuQ2xpZW50SWRcbiAgICAgICAgICB9KTtcbiAgICAgICAgICBjb25zb2xlLmxvZyhg4pyFIEZvdW5kIENvZ25pdG8gVXNlciBQb29sIENsaWVudDogJHt0YXJnZXRDbGllbnROYW1lfSAoJHtjbGllbnQuQ2xpZW50SWR9KWApO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUubG9nKGDinYwgRXJyb3IgY2hlY2tpbmcgQ29nbml0byBVc2VyIFBvb2xzOiAke2Vycm9yfWApO1xuICAgIH1cbiAgfVxuXG4gIHByaXZhdGUgYXN5bmMgZmluZFNOU1RvcGljcyhyZXNvdXJjZXM6IFJlc291cmNlVG9JbXBvcnRbXSk6IFByb21pc2U8dm9pZD4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB0b3BpY3MgPSBhd2FpdCB0aGlzLnNucy5saXN0VG9waWNzKCkucHJvbWlzZSgpO1xuICAgICAgY29uc3QgdGFyZ2V0VG9waWNOYW1lID0gYCR7dGhpcy5wcm9qZWN0TmFtZX0tbm90aWZpY2F0aW9ucy0ke3RoaXMuZW52aXJvbm1lbnR9YDtcbiAgICAgIFxuICAgICAgY29uc3QgdG9waWMgPSB0b3BpY3MuVG9waWNzPy5maW5kKHQgPT4gdC5Ub3BpY0Fybj8uaW5jbHVkZXModGFyZ2V0VG9waWNOYW1lKSk7XG4gICAgICBpZiAodG9waWMgJiYgdG9waWMuVG9waWNBcm4pIHtcbiAgICAgICAgcmVzb3VyY2VzLnB1c2goe1xuICAgICAgICAgIHJlc291cmNlVHlwZTogJ0FXUzo6U05TOjpUb3BpYycsXG4gICAgICAgICAgbG9naWNhbElkOiAnTm90aWZpY2F0aW9uVG9waWMnLFxuICAgICAgICAgIHBoeXNpY2FsUmVzb3VyY2VJZDogdG9waWMuVG9waWNBcm5cbiAgICAgICAgfSk7XG4gICAgICAgIGNvbnNvbGUubG9nKGDinIUgRm91bmQgU05TIFRvcGljOiAke3RhcmdldFRvcGljTmFtZX1gKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5sb2coYOKdjCBFcnJvciBjaGVja2luZyBTTlMgVG9waWNzOiAke2Vycm9yfWApO1xuICAgIH1cbiAgfVxuXG4gIHByaXZhdGUgYXN5bmMgZmluZExhbWJkYUZ1bmN0aW9ucyhyZXNvdXJjZXM6IFJlc291cmNlVG9JbXBvcnRbXSk6IFByb21pc2U8dm9pZD4ge1xuICAgIGNvbnN0IGZ1bmN0aW9uTmFtZXMgPSBbXG4gICAgICBgJHt0aGlzLnByb2plY3ROYW1lfS1jdXN0b20tbWVzc2FnZS0ke3RoaXMuZW52aXJvbm1lbnR9YCxcbiAgICAgIGAke3RoaXMucHJvamVjdE5hbWV9LWFpLXByb2Nlc3NpbmctJHt0aGlzLmVudmlyb25tZW50fWAsXG4gICAgICBgJHt0aGlzLnByb2plY3ROYW1lfS1ub3RpZmljYXRpb25zLSR7dGhpcy5lbnZpcm9ubWVudH1gXG4gICAgXTtcblxuICAgIGZvciAoY29uc3QgZnVuY3Rpb25OYW1lIG9mIGZ1bmN0aW9uTmFtZXMpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGF3YWl0IHRoaXMubGFtYmRhLmdldEZ1bmN0aW9uKHsgRnVuY3Rpb25OYW1lOiBmdW5jdGlvbk5hbWUgfSkucHJvbWlzZSgpO1xuICAgICAgICBjb25zdCBsb2dpY2FsSWQgPSBmdW5jdGlvbk5hbWUuaW5jbHVkZXMoJ2N1c3RvbS1tZXNzYWdlJykgPyAnQ3VzdG9tTWVzc2FnZUZ1bmN0aW9uJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgZnVuY3Rpb25OYW1lLmluY2x1ZGVzKCdhaS1wcm9jZXNzaW5nJykgPyAnQWlQcm9jZXNzaW5nRnVuY3Rpb24nIDogJ05vdGlmaWNhdGlvbkZ1bmN0aW9uJztcbiAgICAgICAgcmVzb3VyY2VzLnB1c2goe1xuICAgICAgICAgIHJlc291cmNlVHlwZTogJ0FXUzo6TGFtYmRhOjpGdW5jdGlvbicsXG4gICAgICAgICAgbG9naWNhbElkLFxuICAgICAgICAgIHBoeXNpY2FsUmVzb3VyY2VJZDogZnVuY3Rpb25OYW1lXG4gICAgICAgIH0pO1xuICAgICAgICBjb25zb2xlLmxvZyhg4pyFIEZvdW5kIExhbWJkYSBmdW5jdGlvbjogJHtmdW5jdGlvbk5hbWV9YCk7XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmxvZyhg4p2MIExhbWJkYSBmdW5jdGlvbiBub3QgZm91bmQ6ICR7ZnVuY3Rpb25OYW1lfWApO1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIGFzeW5jIGdlbmVyYXRlSW1wb3J0RmlsZShyZXNvdXJjZXM6IFJlc291cmNlVG9JbXBvcnRbXSk6IFByb21pc2U8dm9pZD4ge1xuICAgIGlmIChyZXNvdXJjZXMubGVuZ3RoID09PSAwKSB7XG4gICAgICBjb25zb2xlLmxvZygn8J+OiSBObyBleGlzdGluZyByZXNvdXJjZXMgZm91bmQgdGhhdCBuZWVkIGltcG9ydGluZycpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGNvbnN0IGltcG9ydERhdGEgPSB7XG4gICAgICB2ZXJzaW9uOiAxLFxuICAgICAgcmVzb3VyY2VzOiByZXNvdXJjZXMubWFwKHIgPT4gKHtcbiAgICAgICAgcmVzb3VyY2VUeXBlOiByLnJlc291cmNlVHlwZSxcbiAgICAgICAgbG9naWNhbFJlc291cmNlSWQ6IHIubG9naWNhbElkLFxuICAgICAgICByZXNvdXJjZUlkZW50aWZpZXI6IHtcbiAgICAgICAgICBbdGhpcy5nZXRSZXNvdXJjZUlkZW50aWZpZXJLZXkoci5yZXNvdXJjZVR5cGUpXTogci5waHlzaWNhbFJlc291cmNlSWRcbiAgICAgICAgfVxuICAgICAgfSkpXG4gICAgfTtcblxuICAgIGNvbnN0IGltcG9ydEZpbGUgPSBgaW1wb3J0LSR7dGhpcy5lbnZpcm9ubWVudH0uanNvbmA7XG4gICAgcmVxdWlyZSgnZnMnKS53cml0ZUZpbGVTeW5jKGltcG9ydEZpbGUsIEpTT04uc3RyaW5naWZ5KGltcG9ydERhdGEsIG51bGwsIDIpKTtcbiAgICBjb25zb2xlLmxvZyhg8J+ThCBHZW5lcmF0ZWQgaW1wb3J0IGZpbGU6ICR7aW1wb3J0RmlsZX1gKTtcbiAgICBjb25zb2xlLmxvZyhg8J+TiyBGb3VuZCAke3Jlc291cmNlcy5sZW5ndGh9IHJlc291cmNlcyB0byBpbXBvcnRgKTtcbiAgfVxuXG4gIHByaXZhdGUgZ2V0UmVzb3VyY2VJZGVudGlmaWVyS2V5KHJlc291cmNlVHlwZTogc3RyaW5nKTogc3RyaW5nIHtcbiAgICBzd2l0Y2ggKHJlc291cmNlVHlwZSkge1xuICAgICAgY2FzZSAnQVdTOjpEeW5hbW9EQjo6VGFibGUnOiByZXR1cm4gJ1RhYmxlTmFtZSc7XG4gICAgICBjYXNlICdBV1M6OlMzOjpCdWNrZXQnOiByZXR1cm4gJ0J1Y2tldE5hbWUnO1xuICAgICAgY2FzZSAnQVdTOjpDb2duaXRvOjpVc2VyUG9vbCc6IHJldHVybiAnVXNlclBvb2xJZCc7XG4gICAgICBjYXNlICdBV1M6OkNvZ25pdG86OlVzZXJQb29sQ2xpZW50JzogcmV0dXJuICdDbGllbnRJZCc7XG4gICAgICBjYXNlICdBV1M6OlNOUzo6VG9waWMnOiByZXR1cm4gJ1RvcGljQXJuJztcbiAgICAgIGNhc2UgJ0FXUzo6TGFtYmRhOjpGdW5jdGlvbic6IHJldHVybiAnRnVuY3Rpb25OYW1lJztcbiAgICAgIGRlZmF1bHQ6IHJldHVybiAnUGh5c2ljYWxSZXNvdXJjZUlkJztcbiAgICB9XG4gIH1cbn1cblxuYXN5bmMgZnVuY3Rpb24gbWFpbigpIHtcbiAgY29uc3QgZW52aXJvbm1lbnQgPSBwcm9jZXNzLmFyZ3ZbMl0gfHwgJ3Byb2R1Y3Rpb24nO1xuICBjb25zdCByZWdpb24gPSBlbnZpcm9ubWVudCA9PT0gJ3Byb2R1Y3Rpb24nID8gJ3VzLWVhc3QtMScgOiAndXMtd2VzdC0yJztcbiAgY29uc3QgcHJvamVjdE5hbWUgPSAnZ2FtZWZsZXgnO1xuXG4gIGNvbnNvbGUubG9nKGDwn5qAIFN0YXJ0aW5nIHJlc291cmNlIGltcG9ydCBmb3IgJHtlbnZpcm9ubWVudH0gZW52aXJvbm1lbnQgaW4gJHtyZWdpb259YCk7XG4gIFxuICBjb25zdCBpbXBvcnRlciA9IG5ldyBSZXNvdXJjZUltcG9ydGVyKHJlZ2lvbiwgZW52aXJvbm1lbnQsIHByb2plY3ROYW1lKTtcbiAgY29uc3QgcmVzb3VyY2VzID0gYXdhaXQgaW1wb3J0ZXIuZmluZEV4aXN0aW5nUmVzb3VyY2VzKCk7XG4gIGF3YWl0IGltcG9ydGVyLmdlbmVyYXRlSW1wb3J0RmlsZShyZXNvdXJjZXMpO1xuICBcbiAgaWYgKHJlc291cmNlcy5sZW5ndGggPiAwKSB7XG4gICAgY29uc29sZS5sb2coJ1xcbvCfk50gTmV4dCBzdGVwczonKTtcbiAgICBjb25zb2xlLmxvZygnMS4gUmV2aWV3IHRoZSBnZW5lcmF0ZWQgaW1wb3J0IGZpbGUnKTtcbiAgICBjb25zb2xlLmxvZyhgMi4gUnVuOiBjZGsgaW1wb3J0IC0tcmVzb3VyY2UtbWFwcGluZyBpbXBvcnQtJHtlbnZpcm9ubWVudH0uanNvbiAke3Byb2plY3ROYW1lfS0ke2Vudmlyb25tZW50fWApO1xuICB9XG59XG5cbmlmIChyZXF1aXJlLm1haW4gPT09IG1vZHVsZSkge1xuICBtYWluKCkuY2F0Y2goY29uc29sZS5lcnJvcik7XG59XG4iXX0=