#!/usr/bin/env node

/**
 * Remove AWS SDK dependencies from Lambda function package.json files
 * since they should be provided by the Lambda layer
 */

const fs = require('fs');
const path = require('path');

const SRC_DIR = path.join(__dirname, '../src');

// AWS SDK packages that should be removed from individual functions
const AWS_SDK_PACKAGES = [
  '@aws-sdk/client-cognito-identity-provider',
  '@aws-sdk/client-dynamodb',
  '@aws-sdk/client-s3',
  '@aws-sdk/client-secrets-manager',
  '@aws-sdk/client-sns',
  '@aws-sdk/lib-dynamodb',
  '@aws-sdk/s3-request-presigner'
];

// Get all directories in src (excluding layers)
const lambdaDirs = fs.readdirSync(SRC_DIR)
  .filter(dir => {
    const fullPath = path.join(SRC_DIR, dir);
    return fs.statSync(fullPath).isDirectory() && dir !== 'layers';
  });

console.log('Found Lambda functions:', lambdaDirs);

lambdaDirs.forEach(dir => {
  const packageJsonPath = path.join(SRC_DIR, dir, 'package.json');
  
  if (fs.existsSync(packageJsonPath)) {
    console.log(`\nProcessing ${dir}/package.json...`);
    
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    if (packageJson.dependencies) {
      let removed = [];
      
      AWS_SDK_PACKAGES.forEach(pkg => {
        if (packageJson.dependencies[pkg]) {
          delete packageJson.dependencies[pkg];
          removed.push(pkg);
        }
      });
      
      if (removed.length > 0) {
        console.log(`  Removed: ${removed.join(', ')}`);
        fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');
      } else {
        console.log(`  No AWS SDK dependencies to remove`);
      }
    }
  } else {
    console.log(`  No package.json found in ${dir}`);
  }
});

console.log('\nDone! AWS SDK dependencies removed from Lambda functions.');
console.log('They will now be provided by the Lambda layer.');
