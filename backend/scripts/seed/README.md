# GameFlex Database Seeder

A comprehensive TypeScript-based seeding system for GameFlex development and testing.

## Features

- **Selective Seeding**: Seed only specific components instead of everything
- **Smart Force Mode**: Only clears data for components being reseeded
- **AWS Integration**: Native AWS SDK for DynamoDB, Cognito, and S3
- **Media Management**: Uploads 105+ gaming images to S3 with proper CDN URLs
- **Authentication Ready**: Creates proper Cognito + DynamoDB user records

## Quick Start

```bash
# Seed everything (default)
npm run seed:dev

# Clear all data and seed everything
npm run seed:dev:force

# Seed specific components
npm run seed:cognito    # Cognito users only
npm run seed:users      # Users table only
npm run seed:channels   # Channels only
npm run seed:media      # Media/images only
npm run seed:posts      # Posts only
npm run seed:engagement # Comments, likes, follows
```

## Advanced Usage

```bash
# Combine multiple components
npm run seed:dev -- --users --posts --engagement

# Force clear specific components before seeding
npm run seed:dev -- --force --cognito --users

# Get help
npm run seed:help
```

## Command Line Options

| Option | Description |
|--------|-------------|
| `--force` | Clear existing data before seeding |
| `--cognito` | Seed only Cognito users |
| `--users` | Seed only Users table |
| `--channels` | Seed only Channels table |
| `--media` | Seed only Media (upload images to S3) |
| `--posts` | Seed only Posts table |
| `--engagement` | Seed only engagement data (comments, likes, follows) |
| `--help` | Show help message |

## Smart Force Mode

When using `--force` with specific components, only the relevant data is cleared:

- `--force --users`: Clears Users, UserAuths, UserProfiles tables
- `--force --media`: Clears Media table and S3 bucket
- `--force --engagement`: Clears Comments, Likes, Follows, Reflexes tables
- `--force --posts`: Clears Posts table only
- `--force --channels`: Clears Channels, ChannelMembers tables
- `--force --cognito`: Clears Cognito users only

## What Gets Seeded

### Authentication
- **5 Cognito users** with permanent passwords (no force change required)
- **Corresponding DynamoDB Users** with proper UserAuth entries
- **<EMAIL>** / **DevPassword123!** (auto-populated in frontend)

### Content
- **42 total users** across all authentication platforms
- **5 specific gaming channels** with proper icons (Fortnite, Valorant, Minecraft, League of Legends, Call of Duty)
- **85 posts** with realistic gaming content (25+ in Call of Duty channel)
- **105 images** uploaded to S3 with CDN URLs
- **5 channel icons** uploaded to S3 with proper paths
- **~150 comments, ~300 likes, ~100 follows, ~735 reflexes**

### Media Integration
- All posts linked to images via `mediaId`
- S3 bucket: `gameflex-development-media`
- User-specific S3 paths: `media/user/{userId}/{fileName}`
- Channel icon S3 paths: `media/channels/{channelId}/icon/{fileName}`
- Staging paths: `staging/media/{userId}/{mediaId}.jpg`
- CDN URLs: `dev.media.gameflex.io/media/user/{userId}/{fileName}`
- Channel icon URLs: `dev.media.gameflex.io/media/channels/{channelId}/icon/{fileName}`
- Status: All media marked as `approved`
- Proper schema with `url`, `final_url`, `s3Key`, `final_s3_key`, etc.

## Development Workflow

```bash
# Initial setup
npm run seed:dev:force

# Fix authentication issues
npm run seed:dev -- --force --cognito --users

# Add more content
npm run seed:dev -- --posts --engagement

# Reset just media
npm run seed:dev -- --force --media

# Quick channel updates
npm run seed:channels
```

## Data Structure

### Users
- Platform prefixes: `[twitch]`, `[kick]`, `[xbox]`, `[apple]`, `[gf]`
- Diverse usernames and realistic bios
- Proper authentication flow integration

### Channels
- **Fortnite** (ID: 40000000-0000-0000-0000-000000000001): Battle royale tips and tricks
- **Valorant** (ID: 40000000-0000-0000-0000-000000000002): Tactical FPS discussions
- **Minecraft** (ID: 40000000-0000-0000-0000-000000000003): Creative builds and survival
- **League of Legends** (ID: 40000000-0000-0000-0000-000000000004): MOBA strategies
- **Call of Duty** (ID: 40000000-0000-0000-0000-000000000005): Tactical warfare (25+ posts)
- Each channel has proper icon media with S3 integration

### Posts
- 60 general gaming posts
- 25 Call of Duty specific posts
- All posts have attached images
- Realistic engagement metrics

### Reflexes (Image Replies)
- Created based on post reflex counts
- Each reflex linked to random existing media
- `reflexType: 'custom_image'` to show actual images (not flare effects)
- Proper postId + id composite key structure
- Users can reply to posts with images
- All reflexes have `likes: 0` and proper timestamps

### Media
- 105 seed photos from `backend/assets/seed_photos/`
- User-specific S3 organization: `media/user/{userId}/{fileName}`
- Complete schema: `fileName`, `fileType`, `fileSize`, `mediaType`, `userId`
- S3 keys: `s3Key`, `final_s3_key`, `staging_s3_key`
- URLs: `url`, `final_url` with proper CDN paths
- Status: `approved` for immediate use
- Bucket: `gameflex-development-media`

## Troubleshooting

### Authentication Issues
```bash
# Recreate auth users
npm run seed:dev -- --force --cognito --users
```

### Missing Images
```bash
# Reupload all media
npm run seed:dev -- --force --media
```

### Empty Database
```bash
# Full reset
npm run seed:dev:force
```
