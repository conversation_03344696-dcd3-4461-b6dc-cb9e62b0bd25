#!/usr/bin/env node

import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, PutCommand, ScanCommand, DeleteCommand, GetCommand } from '@aws-sdk/lib-dynamodb';
import { CognitoIdentityProviderClient, AdminCreateUserCommand, ListUsersCommand, AdminDeleteUserCommand, ListUserPoolsCommand, AdminSetUserPasswordCommand } from '@aws-sdk/client-cognito-identity-provider';
import { S3Client, ListObjectsV2Command, DeleteObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import { SecretsManagerClient, UpdateSecretCommand } from '@aws-sdk/client-secrets-manager';
import * as fs from 'fs';
import { promises as fsPromises } from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.development' });

interface SeedConfig {
  environment: string;
  region: string;
  stackPrefix: string;
  userPoolId: string;
  s3Bucket: string;
  mediaUrl: string;
  tables: {
    users: string;
    userAuths: string;
    userProfiles: string;
    posts: string;
    media: string;
    comments: string;
    likes: string;
    follows: string;
    channels: string;
    channelMembers: string;
    reflexes: string;
  };
}

interface SeedData {
  users: any;
  posts: any;
  comments: any;
  channels: any;
}

interface SeedOptions {
  force?: boolean;
  cognito?: boolean;
  users?: boolean;
  channels?: boolean;
  media?: boolean;
  posts?: boolean;
  engagement?: boolean;
  all?: boolean;
}

class GameFlexSeeder {
  private dynamoClient: DynamoDBDocumentClient;
  private cognitoClient: CognitoIdentityProviderClient;
  private s3Client: S3Client;
  private secretsClient: SecretsManagerClient;
  private config: SeedConfig;
  private seedData: SeedData;
  private options: SeedOptions;

  constructor(options: SeedOptions = {}) {
    this.options = { all: true, ...options }; // Default to seeding everything

    const region = process.env.AWS_REGION || 'us-west-2';

    this.dynamoClient = DynamoDBDocumentClient.from(new DynamoDBClient({ region }));
    this.cognitoClient = new CognitoIdentityProviderClient({ region });
    this.s3Client = new S3Client({ region });
    this.secretsClient = new SecretsManagerClient({ region });

    this.config = this.loadConfig();
    this.seedData = this.loadSeedData();
  }

  private loadConfig(): SeedConfig {
    const environment = process.env.ENVIRONMENT || 'development';
    const stackPrefix = `gameflex-${environment}`;

    return {
      environment,
      region: process.env.AWS_REGION || 'us-west-2',
      stackPrefix,
      userPoolId: '', // Will be discovered dynamically
      s3Bucket: `${stackPrefix}-media`,
      mediaUrl: process.env.MEDIA_URL || 'dev.media.gameflex.io',
      tables: {
        users: `${stackPrefix}-Users`,
        userAuths: `${stackPrefix}-UserAuths`,
        userProfiles: `${stackPrefix}-UserProfiles`,
        posts: `${stackPrefix}-Posts`,
        media: `${stackPrefix}-Media`,
        comments: `${stackPrefix}-Comments`,
        likes: `${stackPrefix}-Likes`,
        follows: `${stackPrefix}-Follows`,
        channels: `${stackPrefix}-Channels`,
        channelMembers: `${stackPrefix}-ChannelMembers`,
        reflexes: `${stackPrefix}-Reflexes`,
      }
    };
  }

  private async discoverUserPoolId(): Promise<string> {
    try {
      const response = await this.cognitoClient.send(new ListUserPoolsCommand({
        MaxResults: 60
      }));

      if (response.UserPools) {
        const targetPool = response.UserPools.find(pool =>
          pool.Name?.includes('gameflex') && pool.Name?.includes(this.config.environment)
        );

        if (targetPool && targetPool.Id) {
          this.log(`Found User Pool: ${targetPool.Name} (${targetPool.Id})`);
          return targetPool.Id;
        }
      }

      throw new Error(`No User Pool found for environment: ${this.config.environment}`);
    } catch (error) {
      this.log(`Error discovering User Pool ID: ${error}`, 'error');
      throw error;
    }
  }

  private loadSeedData(): SeedData {
    const dataDir = path.join(__dirname, 'data');

    return {
      users: JSON.parse(fs.readFileSync(path.join(dataDir, 'users.json'), 'utf8')),
      posts: JSON.parse(fs.readFileSync(path.join(dataDir, 'posts.json'), 'utf8')),
      comments: JSON.parse(fs.readFileSync(path.join(dataDir, 'comments.json'), 'utf8')),
      channels: JSON.parse(fs.readFileSync(path.join(dataDir, 'channels.json'), 'utf8')),
    };
  }

  private log(message: string, type: 'info' | 'success' | 'error' | 'warn' = 'info') {
    const colors = {
      info: '\x1b[36m[INFO]\x1b[0m',
      success: '\x1b[32m[SUCCESS]\x1b[0m',
      error: '\x1b[31m[ERROR]\x1b[0m',
      warn: '\x1b[33m[WARN]\x1b[0m'
    };
    console.log(`${colors[type]} ${message}`);
  }

  private getRandomElement<T>(array: T[]): T {
    return array[Math.floor(Math.random() * array.length)];
  }

  private generateTimestamp(startDate: string, endDate: string): string {
    const start = new Date(startDate).getTime();
    const end = new Date(endDate).getTime();
    const randomTime = start + Math.random() * (end - start);
    return new Date(randomTime).toISOString();
  }

  private generateUuid(prefix: string, counter: number): string {
    const paddedCounter = counter.toString().padStart(12, '0');
    return `${prefix}-${paddedCounter}`;
  }

  async clearCognitoUsers(): Promise<void> {
    this.log('Clearing Cognito users...');

    try {
      const listResponse = await this.cognitoClient.send(new ListUsersCommand({
        UserPoolId: this.config.userPoolId
      }));

      if (listResponse.Users && listResponse.Users.length > 0) {
        this.log(`Found ${listResponse.Users.length} users to delete`);

        for (const user of listResponse.Users) {
          if (user.Username) {
            await this.cognitoClient.send(new AdminDeleteUserCommand({
              UserPoolId: this.config.userPoolId,
              Username: user.Username
            }));
            this.log(`Deleted Cognito user: ${user.Username}`);
          }
        }
      } else {
        this.log('No Cognito users to delete');
      }
    } catch (error) {
      this.log(`Error clearing Cognito users: ${error}`, 'error');
    }
  }

  async clearDynamoTable(tableName: string): Promise<void> {
    this.log(`Clearing table: ${tableName}`);

    try {
      // Get table key schema to determine what keys to use for deletion
      const tableKeyMap: { [key: string]: string[] } = {
        [`${this.config.stackPrefix}-Comments`]: ['postId', 'id'],
        [`${this.config.stackPrefix}-Likes`]: ['postId', 'userId'],
        [`${this.config.stackPrefix}-Follows`]: ['followerId', 'followingId'],
        [`${this.config.stackPrefix}-Reflexes`]: ['postId', 'id'],
      };

      const keyAttributes = tableKeyMap[tableName] || ['id'];
      const projectionExpression = keyAttributes.join(', ');

      const scanResponse = await this.dynamoClient.send(new ScanCommand({
        TableName: tableName,
        ProjectionExpression: projectionExpression
      }));

      if (scanResponse.Items && scanResponse.Items.length > 0) {
        this.log(`Found ${scanResponse.Items.length} items to delete from ${tableName}`);

        for (const item of scanResponse.Items) {
          const key: any = {};
          for (const keyAttr of keyAttributes) {
            key[keyAttr] = item[keyAttr];
          }

          await this.dynamoClient.send(new DeleteCommand({
            TableName: tableName,
            Key: key
          }));
        }

        this.log(`Cleared ${scanResponse.Items.length} items from ${tableName}`, 'success');
      } else {
        this.log(`Table ${tableName} is already empty`);
      }
    } catch (error) {
      this.log(`Error clearing table ${tableName}: ${error}`, 'error');
    }
  }

  async clearS3Bucket(): Promise<void> {
    this.log(`Clearing S3 bucket: ${this.config.s3Bucket}`);

    try {
      const listResponse = await this.s3Client.send(new ListObjectsV2Command({
        Bucket: this.config.s3Bucket
      }));

      if (listResponse.Contents && listResponse.Contents.length > 0) {
        this.log(`Found ${listResponse.Contents.length} objects to delete`);

        for (const object of listResponse.Contents) {
          if (object.Key) {
            await this.s3Client.send(new DeleteObjectCommand({
              Bucket: this.config.s3Bucket,
              Key: object.Key
            }));
          }
        }

        this.log(`Cleared ${listResponse.Contents.length} objects from S3`, 'success');
      } else {
        this.log('S3 bucket is already empty');
      }
    } catch (error) {
      this.log(`Error clearing S3 bucket: ${error}`, 'error');
    }
  }

  async clearAllData(): Promise<void> {
    this.log('🗑️  Starting force mode - clearing all existing data...');

    // Clear Cognito users
    await this.clearCognitoUsers();

    // Clear DynamoDB tables
    const tableNames = Object.values(this.config.tables);
    for (const tableName of tableNames) {
      await this.clearDynamoTable(tableName);
    }

    // Clear S3 bucket
    await this.clearS3Bucket();

    this.log('🗑️  All existing data has been cleared!', 'success');
  }

  async clearSelectiveData(): Promise<void> {
    this.log('🗑️  Starting selective force mode - clearing data for selected components...');

    // Clear Cognito users only if we're seeding cognito
    if (this.options.cognito || this.options.all) {
      await this.clearCognitoUsers();
    }

    // Clear specific tables based on what we're seeding
    const tablesToClear: string[] = [];

    if (this.options.users || this.options.all) {
      tablesToClear.push(this.config.tables.users, this.config.tables.userAuths, this.config.tables.userProfiles);
    }

    if (this.options.channels || this.options.all) {
      tablesToClear.push(this.config.tables.channels, this.config.tables.channelMembers);
    }

    if (this.options.media || this.options.all) {
      tablesToClear.push(this.config.tables.media);
      // Also clear S3 bucket for media
      await this.clearS3Bucket();
    }

    if (this.options.posts || this.options.all) {
      tablesToClear.push(this.config.tables.posts);
    }

    if (this.options.engagement || this.options.all) {
      tablesToClear.push(this.config.tables.comments, this.config.tables.likes, this.config.tables.follows, this.config.tables.reflexes);
    }

    // Remove duplicates and clear tables
    const uniqueTables = [...new Set(tablesToClear)];
    for (const table of uniqueTables) {
      await this.clearDynamoTable(table);
    }

    // If clearing everything, also clear S3 (unless already done above)
    if (this.options.all && !this.options.media) {
      await this.clearS3Bucket();
    }

    this.log('🗑️  Selective data clearing completed!', 'success');
  }

  async seedCognitoUsers(): Promise<void> {
    this.log('Creating Cognito users...');

    for (const cognitoUser of this.seedData.users.cognitoUsers) {
      try {
        // Create user with temporary password
        await this.cognitoClient.send(new AdminCreateUserCommand({
          UserPoolId: this.config.userPoolId,
          Username: cognitoUser.email,
          UserAttributes: [
            { Name: 'email', Value: cognitoUser.email },
            { Name: 'email_verified', Value: 'true' },
            { Name: 'name', Value: cognitoUser.displayName }
          ],
          TemporaryPassword: 'TempPassword123!',
          MessageAction: 'SUPPRESS'
        }));

        // Set permanent password to avoid force password change
        await this.cognitoClient.send(new AdminSetUserPasswordCommand({
          UserPoolId: this.config.userPoolId,
          Username: cognitoUser.email,
          Password: cognitoUser.password,
          Permanent: true
        }));

        this.log(`✅ Created Cognito user: ${cognitoUser.email}`, 'success');
      } catch (error) {
        this.log(`❌ Failed to create Cognito user ${cognitoUser.email}: ${error}`, 'error');
      }
    }
  }

  async seedUsers(): Promise<void> {
    this.log('Seeding Users table...');

    let userCounter = 1; // Start from 1
    const createdUsers: any[] = [];

    // Add Cognito users to DynamoDB Users table first
    for (const cognitoUser of this.seedData.users.cognitoUsers) {
      const timestamp = new Date().toISOString();
      const user = {
        id: this.generateUuid('********-0000-0000-0000', userCounter),
        username: cognitoUser.email.split('@')[0], // Use email prefix as username
        displayName: cognitoUser.displayName,
        email: cognitoUser.email,
        bio: userCounter === 1 ? "Development account for testing GameFlex features" :
          userCounter === 2 ? "Administrator account with full access" :
            "Test user account",
        isActive: true,
        isVerified: true,
        createdAt: timestamp,
        updatedAt: timestamp
      };

      try {
        await this.dynamoClient.send(new PutCommand({
          TableName: this.config.tables.users,
          Item: user
        }));

        createdUsers.push(user);

        // Create corresponding UserAuth entry for email authentication
        const userAuth = {
          id: this.generateUuid('*************-0000-0000', userCounter),
          userId: user.id,
          provider: 'email',
          providerUserId: cognitoUser.email,
          email: cognitoUser.email,
          isActive: true,
          isPrimary: true,
          isVerified: true,
          createdAt: timestamp,
          updatedAt: timestamp
        };

        await this.dynamoClient.send(new PutCommand({
          TableName: this.config.tables.userAuths,
          Item: userAuth
        }));

        this.log(`✅ Added Cognito user: ${user.username}`, 'success');
        userCounter++;
      } catch (error) {
        this.log(`❌ Failed to add Cognito user ${user.username}: ${error}`, 'error');
      }
    }

    // Generate platform users (continue counter from Cognito users)
    const platforms = ['twitch', 'kick', 'xbox', 'apple', 'email'];
    const platformCounts = { twitch: 8, kick: 6, xbox: 5, apple: 3, email: 15 };

    for (const platform of platforms) {
      const count = platformCounts[platform as keyof typeof platformCounts];
      const usernames = this.seedData.users.usernames[platform];

      for (let i = 0; i < count; i++) {
        const baseUsername = this.getRandomElement(usernames) as string;
        const username = `${baseUsername.toLowerCase()}${Math.floor(Math.random() * 1000)}`;
        const bio = this.getRandomElement(this.seedData.users.bios) as string;
        const timestamp = this.generateTimestamp('2024-01-01', '2024-12-20');

        const displayName = platform === 'email'
          ? `[gf]${username}`
          : `[${platform}]${baseUsername}${Math.floor(Math.random() * 1000)}`;

        const user = {
          id: this.generateUuid('********-0000-0000-0000', userCounter),
          username,
          displayName,
          bio,
          isActive: true,
          isVerified: Math.random() > 0.3, // 70% verified
          createdAt: timestamp,
          updatedAt: timestamp
        };

        try {
          await this.dynamoClient.send(new PutCommand({
            TableName: this.config.tables.users,
            Item: user
          }));

          createdUsers.push(user);
          this.log(`✅ Added ${platform} user: ${user.username}`, 'success');
          userCounter++;
        } catch (error) {
          this.log(`❌ Failed to add ${platform} user ${user.username}: ${error}`, 'error');
        }
      }
    }

    this.log(`✅ Created ${createdUsers.length} users total`, 'success');
  }

  async seedChannels(): Promise<void> {
    this.log('Seeding Channels and uploading channel icons...');

    // First, upload channel icons to S3 and create media records
    const channelIconsDir = path.join(__dirname, '../../assets/media/channels/icons');

    for (const iconData of this.seedData.channels.channelIcons) {
      const iconPath = path.join(channelIconsDir, iconData.fileName);

      try {
        // Read the icon file
        const fileBuffer = await fsPromises.readFile(iconPath);

        // Upload to S3
        await this.s3Client.send(new PutObjectCommand({
          Bucket: this.config.s3Bucket,
          Key: iconData.s3Key,
          Body: fileBuffer,
          ContentType: iconData.fileType,
          CacheControl: 'max-age=31536000' // 1 year cache
        }));

        // Create media record with proper URLs
        const timestamp = new Date().toISOString();
        const mediaRecord = {
          ...iconData,
          bucketName: this.config.s3Bucket,
          url: `https://${this.config.mediaUrl}/${iconData.s3Key}`,
          final_url: `https://${this.config.mediaUrl}/${iconData.final_s3_key}`,
          createdAt: timestamp,
          updatedAt: timestamp
        };

        await this.dynamoClient.send(new PutCommand({
          TableName: this.config.tables.media,
          Item: mediaRecord
        }));

        this.log(`✅ Uploaded channel icon: ${iconData.fileName}`, 'success');
      } catch (error) {
        this.log(`❌ Failed to upload channel icon ${iconData.fileName}: ${error}`, 'error');
      }
    }

    // Then create channel records
    for (const channel of this.seedData.channels.channels) {
      const timestamp = new Date().toISOString();
      const channelData = {
        ...channel,
        createdAt: timestamp,
        updatedAt: timestamp
      };

      try {
        await this.dynamoClient.send(new PutCommand({
          TableName: this.config.tables.channels,
          Item: channelData
        }));

        this.log(`✅ Added channel: ${channel.name}`, 'success');
      } catch (error) {
        this.log(`❌ Failed to add channel ${channel.name}: ${error}`, 'error');
      }
    }
  }

  async seedMedia(): Promise<void> {
    this.log('Seeding Media (uploading images to S3)...');

    const seedPhotosDir = path.join(__dirname, '../../assets/seed_photos');
    const mediaRecords: any[] = [];

    // Get list of all seed photos
    const photoFiles = fs.readdirSync(seedPhotosDir)
      .filter(file => file.endsWith('.jpg') || file.endsWith('.png') || file.endsWith('.webp'))
      .sort((a, b) => {
        const numA = parseInt(a.split('.')[0]);
        const numB = parseInt(b.split('.')[0]);
        return numA - numB;
      });

    this.log(`Found ${photoFiles.length} seed photos to upload`);

    let mediaCounter = 1;

    for (const photoFile of photoFiles) {
      const filePath = path.join(seedPhotosDir, photoFile);
      const fileBuffer = fs.readFileSync(filePath);
      const fileExtension = path.extname(photoFile);
      const fileName = `seed-${mediaCounter}${fileExtension}`;
      const s3Key = `seed-photos/${fileName}`;
      const mediaId = this.generateUuid('*************-0000-0000', mediaCounter);
      const timestamp = this.generateTimestamp('2024-01-01', '2024-12-20');

      try {
        // Get a random user for the media ownership
        const randomUser = this.getRandomElement(await this.getRandomUsers());
        const userId = randomUser?.id || '********-0000-0000-0000-********0001';

        // Create proper S3 keys and URLs following the expected structure
        const userS3Key = `media/user/${userId}/${fileName}`;
        const stagingS3Key = `staging/media/${userId}/${mediaId}${fileExtension}`;
        const finalUrl = `https://${this.config.mediaUrl}/${userS3Key}`;

        // Upload to S3 using the final user-specific key
        await this.s3Client.send(new PutObjectCommand({
          Bucket: this.config.s3Bucket,
          Key: userS3Key,
          Body: fileBuffer,
          ContentType: this.getMimeType(fileExtension),
          CacheControl: 'max-age=31536000' // 1 year cache
        }));

        // Create media record with proper schema
        const mediaRecord = {
          id: mediaId,
          fileName: fileName,
          fileType: this.getMimeType(fileExtension),
          fileSize: fileBuffer.length,
          mediaType: 'image',
          userId: userId,
          s3Key: userS3Key,
          bucketName: this.config.s3Bucket,
          url: finalUrl,
          status: 'approved',
          staging_s3_key: stagingS3Key,
          final_s3_key: userS3Key,
          final_url: finalUrl,
          createdAt: timestamp,
          updatedAt: timestamp
        };

        await this.dynamoClient.send(new PutCommand({
          TableName: this.config.tables.media,
          Item: mediaRecord
        }));

        mediaRecords.push(mediaRecord);

        if (mediaCounter % 10 === 0) {
          this.log(`✅ Uploaded ${mediaCounter}/${photoFiles.length} images`, 'success');
        }

        mediaCounter++;
      } catch (error) {
        this.log(`❌ Failed to upload ${photoFile}: ${error}`, 'error');
      }
    }

    this.log(`✅ Uploaded ${mediaRecords.length} images to S3 and created media records`, 'success');
    return;
  }

  private getMimeType(extension: string): string {
    const mimeTypes: { [key: string]: string } = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.webp': 'image/webp'
    };
    return mimeTypes[extension.toLowerCase()] || 'image/jpeg';
  }

  private async getRandomUsers(): Promise<any[]> {
    try {
      const usersResponse = await this.dynamoClient.send(new ScanCommand({
        TableName: this.config.tables.users,
        ProjectionExpression: 'id'
      }));
      return usersResponse.Items || [];
    } catch (error) {
      this.log(`Warning: Could not fetch users for media ownership: ${error}`, 'warn');
      return [{ id: '********-0000-0000-0000-********0001' }]; // Fallback to dev user
    }
  }

  async seedPosts(): Promise<void> {
    this.log('Seeding Posts...');

    // Get all users for post authoring
    const usersResponse = await this.dynamoClient.send(new ScanCommand({
      TableName: this.config.tables.users,
      ProjectionExpression: 'id, username'
    }));

    const users = usersResponse.Items || [];
    if (users.length === 0) {
      this.log('No users found for post creation', 'warn');
      return;
    }

    // Get all media for posts
    const mediaResponse = await this.dynamoClient.send(new ScanCommand({
      TableName: this.config.tables.media,
      ProjectionExpression: 'id'
    }));

    const mediaItems = mediaResponse.Items || [];
    if (mediaItems.length === 0) {
      this.log('No media found for post creation', 'warn');
      return;
    }

    // Get all channels
    const channelsResponse = await this.dynamoClient.send(new ScanCommand({
      TableName: this.config.tables.channels,
      ProjectionExpression: 'id, #name',
      ExpressionAttributeNames: { '#name': 'name' }
    }));

    const channels = channelsResponse.Items || [];
    const codChannel = channels.find(c => c.name === 'Call of Duty');

    let postCounter = 1;
    const createdPosts: any[] = [];

    // Create general gaming posts
    for (let i = 0; i < 60; i++) {
      const author = this.getRandomElement(users);
      const content = this.getRandomElement(this.seedData.posts.gamingPosts) as string;
      const timestamp = this.generateTimestamp('2024-01-01', '2024-12-20');
      const channel = Math.random() > 0.3 ? this.getRandomElement(channels) : null; // 70% in channels
      const media = this.getRandomElement(mediaItems); // Every post gets an image

      const post = {
        id: this.generateUuid('10000000-0000-0000-0000', postCounter),
        authorId: author.id,
        userId: author.id,
        content,
        mediaId: media.id,
        likes: Math.floor(Math.random() * 50),
        comments: Math.floor(Math.random() * 20),
        reflexes: Math.floor(Math.random() * 15),
        status: 'published',
        active: true,
        createdAt: timestamp,
        updatedAt: timestamp,
        ...(channel && { channelId: channel.id })
      };

      try {
        await this.dynamoClient.send(new PutCommand({
          TableName: this.config.tables.posts,
          Item: post
        }));

        createdPosts.push(post);
        postCounter++;
      } catch (error) {
        this.log(`❌ Failed to create post: ${error}`, 'error');
      }
    }

    // Create Call of Duty specific posts
    if (codChannel) {
      for (let i = 0; i < 25; i++) {
        const author = this.getRandomElement(users);
        const content = this.getRandomElement(this.seedData.posts.codPosts) as string;
        const timestamp = this.generateTimestamp('2024-01-01', '2024-12-20');
        const media = this.getRandomElement(mediaItems); // Every COD post gets an image

        const post = {
          id: this.generateUuid('10000000-0000-0000-0000', postCounter),
          authorId: author.id,
          userId: author.id,
          content,
          mediaId: media.id,
          channelId: codChannel.id,
          likes: Math.floor(Math.random() * 100), // COD posts get more engagement
          comments: Math.floor(Math.random() * 40),
          reflexes: Math.floor(Math.random() * 25),
          status: 'published',
          active: true,
          createdAt: timestamp,
          updatedAt: timestamp
        };

        try {
          await this.dynamoClient.send(new PutCommand({
            TableName: this.config.tables.posts,
            Item: post
          }));

          createdPosts.push(post);
          postCounter++;
        } catch (error) {
          this.log(`❌ Failed to create COD post: ${error}`, 'error');
        }
      }
    }

    this.log(`✅ Created ${createdPosts.length} posts total`, 'success');
  }

  async seedEngagement(): Promise<void> {
    this.log('Seeding Engagement (comments, likes, follows)...');

    // Get all users and posts
    const [usersResponse, postsResponse] = await Promise.all([
      this.dynamoClient.send(new ScanCommand({
        TableName: this.config.tables.users,
        ProjectionExpression: 'id'
      })),
      this.dynamoClient.send(new ScanCommand({
        TableName: this.config.tables.posts,
        ProjectionExpression: 'id'
      }))
    ]);

    const users = usersResponse.Items || [];
    const posts = postsResponse.Items || [];

    if (users.length === 0 || posts.length === 0) {
      this.log('No users or posts found for engagement creation', 'warn');
      return;
    }

    let commentCounter = 1;
    let likeCounter = 1;
    let followCounter = 1;

    // Create comments
    for (let i = 0; i < 150; i++) {
      const post = this.getRandomElement(posts);
      const user = this.getRandomElement(users);
      const content = this.getRandomElement([
        ...this.seedData.comments.gamingComments,
        ...this.seedData.comments.codComments,
        ...this.seedData.comments.supportiveComments
      ]) as string;
      const timestamp = this.generateTimestamp('2024-01-01', '2024-12-20');

      const comment = {
        id: this.generateUuid('*************-0000-0000', commentCounter),
        postId: post.id,
        userId: user.id,
        content,
        likeCount: Math.floor(Math.random() * 10),
        isActive: true,
        createdAt: timestamp,
        updatedAt: timestamp
      };

      try {
        await this.dynamoClient.send(new PutCommand({
          TableName: this.config.tables.comments,
          Item: comment
        }));
        commentCounter++;
      } catch (error) {
        this.log(`❌ Failed to create comment: ${error}`, 'error');
      }
    }

    // Create likes
    for (let i = 0; i < 300; i++) {
      const post = this.getRandomElement(posts);
      const user = this.getRandomElement(users);
      const timestamp = this.generateTimestamp('2024-01-01', '2024-12-20');

      const like = {
        postId: post.id,
        userId: user.id,
        createdAt: timestamp
      };

      try {
        await this.dynamoClient.send(new PutCommand({
          TableName: this.config.tables.likes,
          Item: like
        }));
        likeCounter++;
      } catch (error) {
        // Ignore duplicate likes (same user liking same post)
        if (!(error as Error).toString().includes('ConditionalCheckFailedException')) {
          this.log(`❌ Failed to create like: ${error}`, 'error');
        }
      }
    }

    // Create follows
    for (let i = 0; i < 100; i++) {
      const follower = this.getRandomElement(users);
      const following = this.getRandomElement(users);

      // Don't let users follow themselves
      if (follower.id === following.id) continue;

      const timestamp = this.generateTimestamp('2024-01-01', '2024-12-20');

      const follow = {
        followerId: follower.id,
        followingId: following.id,
        createdAt: timestamp
      };

      try {
        await this.dynamoClient.send(new PutCommand({
          TableName: this.config.tables.follows,
          Item: follow
        }));
        followCounter++;
      } catch (error) {
        // Ignore duplicate follows
        if (!(error as Error).toString().includes('ConditionalCheckFailedException')) {
          this.log(`❌ Failed to create follow: ${error}`, 'error');
        }
      }
    }

    // Create reflexes (image replies to posts)
    let reflexCounter = 1;

    // Get all media for reflexes
    const mediaResponse = await this.dynamoClient.send(new ScanCommand({
      TableName: this.config.tables.media,
      ProjectionExpression: 'id'
    }));

    const mediaItems = mediaResponse.Items || [];
    if (mediaItems.length === 0) {
      this.log('No media found for reflex creation', 'warn');
    } else {
      // Create reflexes for posts that have reflex counts
      for (const post of posts) {
        // Get the actual post to check reflex count
        const postResponse = await this.dynamoClient.send(new GetCommand({
          TableName: this.config.tables.posts,
          Key: { id: post.id }
        }));

        const fullPost = postResponse.Item;
        if (fullPost && fullPost.reflexes && fullPost.reflexes > 0) {
          // Create the number of reflexes indicated by the post's reflex count
          for (let i = 0; i < fullPost.reflexes; i++) {
            const user = this.getRandomElement(users);
            const media = this.getRandomElement(mediaItems);
            const timestamp = this.generateTimestamp('2024-01-01', '2024-12-20');

            const reflex = {
              id: this.generateUuid('*************-0000-0000', reflexCounter),
              postId: post.id,
              userId: user.id,
              mediaId: media.id,
              reflexType: 'custom_image',
              likes: 0,
              isActive: true,
              createdAt: timestamp,
              updatedAt: timestamp
            };

            try {
              await this.dynamoClient.send(new PutCommand({
                TableName: this.config.tables.reflexes,
                Item: reflex
              }));
              reflexCounter++;
            } catch (error) {
              this.log(`❌ Failed to create reflex: ${error}`, 'error');
            }
          }
        }
      }
    }

    this.log(`✅ Created ~${commentCounter - 1} comments, ~${likeCounter - 1} likes, ~${followCounter - 1} follows, ~${reflexCounter - 1} reflexes`, 'success');
  }

  async run(): Promise<void> {
    try {
      this.log('🚀 Starting GameFlex database seeding...');
      this.log(`Environment: ${this.config.environment}`);
      this.log(`Region: ${this.config.region}`);
      this.log(`Stack Prefix: ${this.config.stackPrefix}`);

      // Discover User Pool ID
      this.config.userPoolId = await this.discoverUserPoolId();

      // Check if force mode is enabled
      if (this.options.force) {
        if (this.options.all) {
          await this.clearAllData();
        } else {
          await this.clearSelectiveData();
        }
      }

      // Start seeding
      this.log('🌱 Starting data seeding...');

      // Seed data selectively based on options
      if (this.options.all || this.options.cognito) {
        await this.seedCognitoUsers();
      }

      if (this.options.all || this.options.users) {
        await this.seedUsers();
      }

      if (this.options.all || this.options.channels) {
        await this.seedChannels();
      }

      if (this.options.all || this.options.media) {
        await this.seedMedia();
      }

      if (this.options.all || this.options.posts) {
        await this.seedPosts();
      }

      if (this.options.all || this.options.engagement) {
        await this.seedEngagement();
      }

      this.log('✅ Database seeding completed successfully!', 'success');

    } catch (error) {
      this.log(`❌ Seeding failed: ${error}`, 'error');
      process.exit(1);
    }
  }
}

// Parse command line arguments
function parseArgs(): SeedOptions {
  const args = process.argv.slice(2);
  const options: SeedOptions = {};

  for (const arg of args) {
    switch (arg) {
      case '--force':
        options.force = true;
        break;
      case '--cognito':
        options.cognito = true;
        options.all = false;
        break;
      case '--users':
        options.users = true;
        options.all = false;
        break;
      case '--channels':
        options.channels = true;
        options.all = false;
        break;
      case '--media':
        options.media = true;
        options.all = false;
        break;
      case '--posts':
        options.posts = true;
        options.all = false;
        break;
      case '--engagement':
        options.engagement = true;
        options.all = false;
        break;
      case '--help':
        console.log(`
GameFlex Database Seeder

Usage: npm run seed:dev [options]

Options:
  --force        Clear all existing data before seeding
  --cognito      Seed only Cognito users
  --users        Seed only Users table
  --channels     Seed only Channels table
  --media        Seed only Media (upload images to S3)
  --posts        Seed only Posts table
  --engagement   Seed only engagement data (comments, likes, follows)
  --help         Show this help message

Examples:
  npm run seed:dev                    # Seed everything
  npm run seed:dev --force            # Clear all data and seed everything
  npm run seed:dev --users --posts    # Seed only users and posts
  npm run seed:dev --force --cognito  # Clear all data and seed only Cognito users
        `);
        process.exit(0);
        break;
    }
  }

  // If no specific options are set, default to all
  if (!options.cognito && !options.users && !options.channels &&
    !options.media && !options.posts && !options.engagement) {
    options.all = true;
  }

  return options;
}

// Run the seeder
if (require.main === module) {
  const options = parseArgs();
  const seeder = new GameFlexSeeder(options);
  seeder.run();
}
