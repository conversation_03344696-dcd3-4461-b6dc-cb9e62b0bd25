#!/bin/bash

# Script to configure API Gateway v2 account settings for CloudWatch logging
# This is a one-time setup required for API Gateway v2 WebSocket logging
#
# Usage: ./setup-apigatewayv2-logging.sh [environment]
# Example: ./setup-apigatewayv2-logging.sh production

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if environment is provided
if [ $# -eq 0 ]; then
    print_error "Environment parameter is required"
    echo "Usage: $0 [environment]"
    echo "Example: $0 production"
    exit 1
fi

ENVIRONMENT=$1
PROJECT_NAME="gameflex"

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
    print_error "Invalid environment: $ENVIRONMENT"
    echo "Valid environments: development, staging, production"
    exit 1
fi

# Set AWS region based on environment
case $ENVIRONMENT in
    "development"|"staging")
        AWS_REGION="us-west-2"
        ;;
    "production")
        AWS_REGION="us-east-1"
        ;;
esac

print_info "Setting up API Gateway v2 account configuration for $ENVIRONMENT environment in $AWS_REGION"

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    print_error "AWS CLI is not installed. Please install it first."
    exit 1
fi

# Check AWS credentials
if ! aws sts get-caller-identity &> /dev/null; then
    print_error "AWS credentials not configured or invalid"
    exit 1
fi

# Get the CloudWatch role ARN from the deployed infrastructure
ROLE_NAME="${PROJECT_NAME}-${ENVIRONMENT}-api-gateway-cloudwatch-role"
print_info "Looking for IAM role: $ROLE_NAME"

ROLE_ARN=$(aws iam get-role --role-name "$ROLE_NAME" --query 'Role.Arn' --output text 2>/dev/null || echo "")

if [ -z "$ROLE_ARN" ]; then
    print_error "CloudWatch role not found: $ROLE_NAME"
    print_info "Please ensure the Terraform infrastructure has been deployed first"
    exit 1
fi

print_success "Found CloudWatch role: $ROLE_ARN"

# Check current API Gateway v2 account configuration
print_info "Checking current API Gateway v2 account configuration..."

CURRENT_CONFIG=$(aws apigatewayv2 get-account --region "$AWS_REGION" 2>/dev/null || echo "{}")

if echo "$CURRENT_CONFIG" | grep -q "CloudwatchRoleArn"; then
    CURRENT_ROLE=$(echo "$CURRENT_CONFIG" | jq -r '.CloudwatchRoleArn // empty')
    if [ "$CURRENT_ROLE" = "$ROLE_ARN" ]; then
        print_success "API Gateway v2 account is already configured with the correct CloudWatch role"
        exit 0
    else
        print_warning "API Gateway v2 account has a different CloudWatch role configured: $CURRENT_ROLE"
        print_info "Updating to use the new role: $ROLE_ARN"
    fi
else
    print_info "No CloudWatch role currently configured for API Gateway v2"
fi

# Update API Gateway v2 account configuration
print_info "Configuring API Gateway v2 account settings..."

UPDATE_RESULT=$(aws apigatewayv2 update-account \
    --region "$AWS_REGION" \
    --cloudwatch-role-arn "$ROLE_ARN" \
    2>&1 || echo "FAILED")

if echo "$UPDATE_RESULT" | grep -q "FAILED"; then
    print_error "Failed to update API Gateway v2 account configuration"
    echo "$UPDATE_RESULT"
    exit 1
fi

print_success "Successfully configured API Gateway v2 account with CloudWatch role"

# Verify the configuration
print_info "Verifying configuration..."

VERIFICATION=$(aws apigatewayv2 get-account --region "$AWS_REGION" --query 'CloudwatchRoleArn' --output text 2>/dev/null || echo "")

if [ "$VERIFICATION" = "$ROLE_ARN" ]; then
    print_success "Configuration verified successfully"
    print_info "API Gateway v2 WebSocket APIs can now use CloudWatch logging"
else
    print_error "Configuration verification failed"
    print_error "Expected: $ROLE_ARN"
    print_error "Got: $VERIFICATION"
    exit 1
fi

print_success "API Gateway v2 CloudWatch logging setup completed for $ENVIRONMENT environment"
print_info "You can now deploy WebSocket APIs with logging enabled in this region"
