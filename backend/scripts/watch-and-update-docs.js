#!/usr/bin/env node

/**
 * Watch for code changes and automatically update API documentation
 * This script monitors Lambda function files and triggers documentation regeneration
 */

const chokidar = require('chokidar');
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

class DocumentationWatcher {
    constructor() {
        this.isGenerating = false;
        this.pendingRegeneration = false;
        this.debounceTimeout = null;
        this.debounceDelay = 2000; // 2 seconds

        // Paths to watch for changes
        this.watchPaths = [
            'src/**/*.ts',
            'terraform/**/*.tf',
            'terraform/**/*.json'
        ];

        // Paths to exclude from watching
        this.ignorePaths = [
            '**/node_modules/**',
            '**/dist/**',
            '**/*.test.ts',
            '**/*.spec.ts',
            '**/tests/**',
            '**/.git/**',
            '**/coverage/**'
        ];
    }

    async start() {
        console.log('🔍 Starting API documentation watcher...');
        console.log('📁 Watching paths:', this.watchPaths);
        console.log('🚫 Ignoring paths:', this.ignorePaths);
        console.log('⏱️  Debounce delay:', this.debounceDelay + 'ms');
        console.log('');

        // Initialize watcher
        const watcher = chokidar.watch(this.watchPaths, {
            ignored: this.ignorePaths,
            ignoreInitial: true,
            persistent: true,
            awaitWriteFinish: {
                stabilityThreshold: 1000,
                pollInterval: 100
            }
        });

        // Set up event handlers
        watcher
            .on('add', (filePath) => this.handleFileChange('added', filePath))
            .on('change', (filePath) => this.handleFileChange('changed', filePath))
            .on('unlink', (filePath) => this.handleFileChange('removed', filePath))
            .on('error', (error) => console.error('❌ Watcher error:', error))
            .on('ready', () => {
                console.log('✅ Documentation watcher is ready and monitoring for changes...');
                console.log('💡 Press Ctrl+C to stop watching\n');
            });

        // Handle graceful shutdown
        process.on('SIGINT', () => {
            console.log('\n🛑 Stopping documentation watcher...');
            watcher.close();
            process.exit(0);
        });

        // Generate initial documentation
        console.log('📝 Generating initial documentation...');
        await this.generateDocumentation();
    }

    handleFileChange(eventType, filePath) {
        const relativePath = path.relative(process.cwd(), filePath);
        console.log(`📄 File ${eventType}: ${relativePath}`);

        // Clear existing timeout
        if (this.debounceTimeout) {
            clearTimeout(this.debounceTimeout);
        }

        // Set new timeout for debounced regeneration
        this.debounceTimeout = setTimeout(() => {
            this.triggerDocumentationUpdate();
        }, this.debounceDelay);
    }

    async triggerDocumentationUpdate() {
        if (this.isGenerating) {
            console.log('⏳ Documentation generation already in progress, queuing update...');
            this.pendingRegeneration = true;
            return;
        }

        console.log('\n🔄 Triggering documentation update...');
        await this.generateDocumentation();

        // Check if another update was queued while generating
        if (this.pendingRegeneration) {
            this.pendingRegeneration = false;
            console.log('🔄 Processing queued documentation update...');
            await this.generateDocumentation();
        }
    }

    async generateDocumentation() {
        if (this.isGenerating) {
            return;
        }

        this.isGenerating = true;
        const startTime = Date.now();

        try {
            console.log('📦 Building TypeScript files...');
            await this.runCommand('npm', ['run', 'build']);

            console.log('📝 Generating API documentation...');
            await this.runCommand('node', ['-r', 'ts-node/register', 'src/documentation/cli.ts', 'generate']);

            const duration = Date.now() - startTime;
            console.log(`✅ Documentation updated successfully in ${duration}ms`);
            console.log('📄 Updated files:');
            console.log('   - .kiro/api-docs/api-documentation.json');
            console.log('   - .kiro/api-docs/routes-summary.md');
            console.log('   - docs/api/openapi.json');
            console.log('   - docs/api/swagger.json');
            console.log('   - docs/api/API_DOCUMENTATION.md');
            console.log('');

        } catch (error) {
            console.error('❌ Error generating documentation:', error.message);
        } finally {
            this.isGenerating = false;
        }
    }

    runCommand(command, args, options = {}) {
        return new Promise((resolve, reject) => {
            const child = spawn(command, args, {
                stdio: 'pipe', // Capture output to reduce noise
                cwd: path.join(__dirname, '..'),
                ...options
            });

            let stdout = '';
            let stderr = '';

            child.stdout?.on('data', (data) => {
                stdout += data.toString();
            });

            child.stderr?.on('data', (data) => {
                stderr += data.toString();
            });

            child.on('close', (code) => {
                if (code === 0) {
                    resolve({ stdout, stderr });
                } else {
                    reject(new Error(`Command failed with exit code ${code}\nStderr: ${stderr}`));
                }
            });

            child.on('error', (error) => {
                reject(error);
            });
        });
    }
}

// Check if chokidar is available
function checkDependencies() {
    try {
        require('chokidar');
        return true;
    } catch (error) {
        console.error('❌ Missing dependency: chokidar');
        console.error('💡 Install it with: npm install --save-dev chokidar');
        return false;
    }
}

async function main() {
    console.log('🚀 GameFlex API Documentation Auto-Updater');
    console.log('==========================================\n');

    if (!checkDependencies()) {
        process.exit(1);
    }

    const watcher = new DocumentationWatcher();
    await watcher.start();
}

if (require.main === module) {
    main().catch((error) => {
        console.error('❌ Fatal error:', error);
        process.exit(1);
    });
}

module.exports = { DocumentationWatcher };