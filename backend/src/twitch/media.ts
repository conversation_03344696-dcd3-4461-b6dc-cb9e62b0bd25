import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { createResponse } from '/opt/nodejs/lambda-helpers';
import { userAuthService } from '/opt/nodejs/user-auth-service';
import { AuthProvider } from '/opt/nodejs/user-auth';
import { SecretsManagerClient, GetSecretValueCommand } from '@aws-sdk/client-secrets-manager';
const fetch = require('node-fetch');

// Initialize AWS clients
const secretsManager = new SecretsManagerClient({ region: process.env.AWS_REGION || 'us-west-2' });

// Twitch API endpoints
const TWITCH_CLIPS_BASE_URL = 'https://api.twitch.tv/helix/clips';
const TWITCH_CLIPS_DOWNLOAD_BASE_URL = 'https://api.twitch.tv/helix/clips/downloads';
const TWITCH_TOKEN_URL = 'https://id.twitch.tv/oauth2/token';

// Cache for Twitch config
let twitchConfig: {
    clientId: string;
    clientSecret: string;
} | null = null;

// TypeScript interfaces for Twitch clips
interface TwitchMediaItem {
    id: string;
    type: 'clip';
    title: string;
    thumbnailUrl: string;
    downloadUrl: string;
    embedUrl: string;
    broadcasterName: string;
    creatorName: string;
    gameId: string;
    language: string;
    viewCount: number;
    createdAt: string;
    duration: number;
    vodOffset: number | null;
    platform: string;
}

interface TwitchClipsApiResponse {
    data: TwitchClip[];
    pagination?: {
        cursor?: string;
    };
}

interface TwitchClipDownload {
    clip_id: string;
    landscape_download_url: string | null;
    portrait_download_url: string | null;
}

interface TwitchClipsDownloadApiResponse {
    data: TwitchClipDownload[];
}

interface TwitchClip {
    id: string;
    url: string;
    embed_url: string;
    broadcaster_id: string;
    broadcaster_name: string;
    creator_id: string;
    creator_name: string;
    video_id: string;
    game_id: string;
    language: string;
    title: string;
    view_count: number;
    created_at: string;
    thumbnail_url: string;
    duration: number;
    vod_offset: number | null;
    is_featured: boolean;
}

interface TwitchMediaResponse {
    clips: TwitchMediaItem[];
    pagination: {
        hasMore: boolean;
        cursor?: string;
    };
}

interface TwitchTokenResponse {
    access_token: string;
    refresh_token: string;
    expires_in: number;
    scope: string[];
    token_type: string;
}

// Helper function to get Twitch config from secrets
const getTwitchConfig = async () => {
    if (twitchConfig) {
        return twitchConfig;
    }

    try {
        const secretName = process.env.TWITCH_SECRET_NAME || 'gameflex-twitch-config-development';
        const command = new GetSecretValueCommand({
            SecretId: secretName,
        });
        const response = await secretsManager.send(command);

        if (!response.SecretString) {
            throw new Error('Secret value is empty');
        }

        const secrets = JSON.parse(response.SecretString);
        twitchConfig = {
            clientId: secrets.clientId || secrets.twitchClientId,
            clientSecret: secrets.clientSecret || secrets.twitchClientSecret
        };

        if (!twitchConfig.clientId || !twitchConfig.clientSecret) {
            throw new Error('Twitch client ID or secret not found in config secret');
        }

        return twitchConfig;
    } catch (error) {
        console.error('Error getting Twitch config:', error);
        throw new Error(`Failed to retrieve Twitch configuration: ${(error as Error).message}`);
    }
};

// Helper function to refresh Twitch token
const refreshTwitchToken = async (userAuth: any): Promise<{ accessToken: string; refreshToken: string; expiresAt: string }> => {
    try {
        if (!userAuth.refreshToken) {
            throw new Error('No refresh token available');
        }

        const config = await getTwitchConfig();
        if (!config) {
            throw new Error('Twitch configuration not available');
        }

        const tokenParams = new URLSearchParams({
            client_id: config.clientId,
            client_secret: config.clientSecret,
            refresh_token: userAuth.refreshToken,
            grant_type: 'refresh_token'
        });

        const response = await fetch(TWITCH_TOKEN_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: tokenParams.toString()
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Twitch token refresh failed: ${response.status} - ${errorText}`);
        }

        const tokenData: TwitchTokenResponse = await response.json();
        const expiresAt = new Date(Date.now() + tokenData.expires_in * 1000).toISOString();

        // Update the UserAuth record with new tokens
        await userAuthService.updateUserAuth({
            id: userAuth.id,
            accessToken: tokenData.access_token,
            refreshToken: tokenData.refresh_token,
            tokenExpiresAt: expiresAt
        });

        console.log('Twitch tokens refreshed successfully for account:', userAuth.id);
        return {
            accessToken: tokenData.access_token,
            refreshToken: tokenData.refresh_token,
            expiresAt
        };
    } catch (error) {
        console.error('Error refreshing Twitch token:', error);
        throw error;
    }
};

// Helper function to get Twitch authentication data for a user
const getTwitchAuthData = async (userId: string) => {
    try {
        // Find Twitch auth by userId (not providerUserId)
        const userAuths = await userAuthService.findUserAuthsByUserId({ userId, activeOnly: true });
        const twitchAuth = userAuths.find(auth => auth.provider === AuthProvider.TWITCH);

        if (!twitchAuth) {
            throw new Error('No Twitch authentication found for user');
        }

        if (!twitchAuth.accessToken) {
            throw new Error('No Twitch access token available');
        }

        // Check if token is expired and refresh if needed
        if (twitchAuth.tokenExpiresAt && new Date(twitchAuth.tokenExpiresAt) <= new Date()) {
            // Token is expired, need to refresh
            const refreshedTokens = await refreshTwitchToken(twitchAuth);
            return {
                accessToken: refreshedTokens.accessToken,
                providerUserId: twitchAuth.providerUserId
            };
        }

        return {
            accessToken: twitchAuth.accessToken,
            providerUserId: twitchAuth.providerUserId
        };
    } catch (error) {
        console.error('Error getting Twitch auth data:', error);
        throw error;
    }
};



// Helper function to fetch download URLs for clips using Twitch's download API
const fetchClipDownloadUrls = async (
    accessToken: string,
    broadcasterUserId: string,
    editorUserId: string,
    clipIds: string[]
): Promise<Map<string, string>> => {
    try {
        if (clipIds.length === 0) {
            return new Map();
        }

        // Limit to 10 clips per request as per Twitch API
        const limitedClipIds = clipIds.slice(0, 10);

        const params = new URLSearchParams({
            broadcaster_id: broadcasterUserId,
            editor_id: editorUserId
        });

        // Add each clip_id as a separate parameter
        limitedClipIds.forEach(clipId => {
            params.append('clip_id', clipId);
        });

        const requestUrl = `${TWITCH_CLIPS_DOWNLOAD_BASE_URL}?${params}`;
        console.log('Fetching Twitch clip download URLs from:', requestUrl);

        const config = await getTwitchConfig();
        const response = await fetch(requestUrl, {
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Client-Id': config.clientId,
                'Accept': 'application/json'
            }
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`Twitch download API error: ${response.status} - ${errorText}`);
            // Return empty map on error, we'll fall back to embed URLs
            return new Map();
        }

        const data: TwitchClipsDownloadApiResponse = await response.json();
        console.log('Twitch download API response:', JSON.stringify(data, null, 2));

        // Create a map of clip_id -> download_url
        const downloadUrls = new Map<string, string>();
        data.data.forEach(download => {
            // Prefer landscape, fall back to portrait
            const downloadUrl = download.landscape_download_url || download.portrait_download_url;
            if (downloadUrl) {
                downloadUrls.set(download.clip_id, downloadUrl);
            }
        });

        return downloadUrls;
    } catch (error) {
        console.error('Error fetching clip download URLs:', error);
        return new Map();
    }
};

// Helper function to fetch Twitch clips from Twitch API
const fetchTwitchClips = async (
    accessToken: string,
    broadcasterUserId: string,
    options: { first: number; after?: string; startedAt?: string; endedAt?: string }
): Promise<{ items: TwitchMediaItem[]; hasMore: boolean; cursor?: string }> => {
    try {
        const params = new URLSearchParams({
            broadcaster_id: broadcasterUserId,
            first: options.first.toString()
        });

        if (options.after) {
            params.append('after', options.after);
        }

        if (options.startedAt) {
            params.append('started_at', options.startedAt);
        }

        if (options.endedAt) {
            params.append('ended_at', options.endedAt);
        }

        const requestUrl = `${TWITCH_CLIPS_BASE_URL}?${params}`;
        console.log('Fetching Twitch clips from:', requestUrl);

        const config = await getTwitchConfig();
        const response = await fetch(requestUrl, {
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Client-Id': config.clientId,
                'Accept': 'application/json'
            }
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Twitch API error: ${response.status} - ${errorText}`);
        }

        const data: TwitchClipsApiResponse = await response.json();

        // Get download URLs for all clips
        const clipIds = data.data.map(clip => clip.id);
        const downloadUrls = await fetchClipDownloadUrls(accessToken, broadcasterUserId, broadcasterUserId, clipIds);

        // Transform Twitch API response to our format
        const items: TwitchMediaItem[] = (data.data || []).map((clip: TwitchClip) => {
            // Try to get the actual download URL, fall back to clip page URL
            const actualDownloadUrl = downloadUrls.get(clip.id);
            const downloadUrl = actualDownloadUrl || clip.url; // Use clip page URL as fallback

            if (actualDownloadUrl) {
                console.log(`Using actual download URL for clip ${clip.id}: ${actualDownloadUrl}`);
            } else {
                console.log(`Using clip page URL for clip ${clip.id}: ${downloadUrl} (requires manual download)`);
            }

            return {
                id: clip.id,
                type: 'clip' as const,
                title: clip.title || 'Untitled Clip',
                thumbnailUrl: clip.thumbnail_url || '',
                downloadUrl,
                embedUrl: clip.embed_url || '',
                broadcasterName: clip.broadcaster_name || 'Unknown Broadcaster',
                creatorName: clip.creator_name || 'Unknown Creator',
                gameId: clip.game_id || '',
                language: clip.language || 'en',
                viewCount: clip.view_count || 0,
                createdAt: clip.created_at || '',
                duration: clip.duration || 0,
                vodOffset: clip.vod_offset,
                platform: 'Twitch'
            };
        });

        return {
            items,
            hasMore: !!data.pagination?.cursor,
            cursor: data.pagination?.cursor
        };
    } catch (error) {
        console.error('Error fetching Twitch clips:', error);
        throw error;
    }
};

// Lambda handler for getting Twitch clips
export const getTwitchClips = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
        // Get user ID from the authorizer context
        const userId = event.requestContext.authorizer?.userId;
        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Get Twitch authentication data
        const twitchAuth = await getTwitchAuthData(userId);

        // Parse query parameters
        const queryParams = event.queryStringParameters || {};
        const first = parseInt(queryParams.first || '20');
        const after = queryParams.after;
        const startedAt = queryParams.started_at;
        const endedAt = queryParams.ended_at;

        // Fetch clips from Twitch API
        const clips = await fetchTwitchClips(
            twitchAuth.accessToken,
            twitchAuth.providerUserId!,
            { first, after, startedAt, endedAt }
        );

        return createResponse(200, {
            clips: clips.items,
            pagination: {
                hasMore: clips.hasMore,
                cursor: clips.cursor
            }
        });

    } catch (error) {
        console.error('GetTwitchClips error:', error);
        return createResponse(500, {
            error: 'Failed to fetch Twitch clips',
            details: (error as Error).message
        });
    }
};

// Main handler function
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    const path = event.path;
    const httpMethod = event.httpMethod;

    try {
        // Route to appropriate handler based on path and method
        if (path === '/twitch/clips' && httpMethod === 'GET') {
            return await getTwitchClips(event);
        } else {
            return createResponse(404, { error: 'Endpoint not found' });
        }
    } catch (error) {
        console.error('Twitch Media Handler Error:', error);
        return createResponse(500, {
            error: 'Internal server error',
            details: (error as Error).message
        });
    }
};
