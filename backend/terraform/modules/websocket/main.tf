# WebSocket API Gateway Module for GameFlex Backend

locals {
  name_prefix = "${var.project_name}-${var.environment}"
}

# Data sources
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

# WebSocket API Gateway
resource "aws_apigatewayv2_api" "websocket" {
  name                       = "${local.name_prefix}-websocket"
  description                = "GameFlex WebSocket API - ${var.environment}"
  protocol_type              = "WEBSOCKET"
  route_selection_expression = "$request.body.action"

  tags = merge(var.tags, {
    Name = "${local.name_prefix}-websocket"
    Type = "WebSocket API"
  })
}

# WebSocket API Gateway Stage
resource "aws_apigatewayv2_stage" "websocket" {
  api_id      = aws_apigatewayv2_api.websocket.id
  name        = var.environment
  auto_deploy = true

  default_route_settings {
    throttling_rate_limit  = 1000
    throttling_burst_limit = 2000
  }

  tags = var.tags
}

# Custom domain for WebSocket API (if provided)
resource "aws_apigatewayv2_domain_name" "websocket" {
  count       = var.websocket_domain_name != null ? 1 : 0
  domain_name = var.websocket_domain_name

  domain_name_configuration {
    certificate_arn = var.websocket_certificate_arn
    endpoint_type   = "REGIONAL"
    security_policy = "TLS_1_2"
  }

  tags = var.tags
}

# API Mapping for custom domain
resource "aws_apigatewayv2_api_mapping" "websocket" {
  count       = var.websocket_domain_name != null ? 1 : 0
  api_id      = aws_apigatewayv2_api.websocket.id
  domain_name = aws_apigatewayv2_domain_name.websocket[0].id
  stage       = aws_apigatewayv2_stage.websocket.id
}

# JWT Authorizer for WebSocket connections
resource "aws_apigatewayv2_authorizer" "jwt" {
  api_id           = aws_apigatewayv2_api.websocket.id
  authorizer_type  = "REQUEST"
  authorizer_uri   = var.connection_authorizer_function.invoke_arn
  name             = "${local.name_prefix}-websocket-authorizer"
  identity_sources = ["route.request.querystring.token"]
}

# Lambda permission for WebSocket API Gateway to invoke authorizer
resource "aws_lambda_permission" "websocket_authorizer" {
  statement_id  = "AllowWebSocketAPIGatewayInvokeAuthorizer"
  action        = "lambda:InvokeFunction"
  function_name = var.connection_authorizer_function.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_apigatewayv2_api.websocket.execution_arn}/*/*"
}

# $connect route
resource "aws_apigatewayv2_route" "connect" {
  api_id    = aws_apigatewayv2_api.websocket.id
  route_key = "$connect"
  
  authorization_type = "CUSTOM"
  authorizer_id      = aws_apigatewayv2_authorizer.jwt.id
  target             = "integrations/${aws_apigatewayv2_integration.connect.id}"
}

# $disconnect route
resource "aws_apigatewayv2_route" "disconnect" {
  api_id    = aws_apigatewayv2_api.websocket.id
  route_key = "$disconnect"
  target    = "integrations/${aws_apigatewayv2_integration.disconnect.id}"
}

# Default route for incoming messages
resource "aws_apigatewayv2_route" "default" {
  api_id    = aws_apigatewayv2_api.websocket.id
  route_key = "$default"
  target    = "integrations/${aws_apigatewayv2_integration.message.id}"
}

# Ping route for connection health
resource "aws_apigatewayv2_route" "ping" {
  api_id    = aws_apigatewayv2_api.websocket.id
  route_key = "ping"
  target    = "integrations/${aws_apigatewayv2_integration.message.id}"
}

# Connect integration
resource "aws_apigatewayv2_integration" "connect" {
  api_id           = aws_apigatewayv2_api.websocket.id
  integration_type = "AWS_PROXY"
  integration_uri  = var.connection_handler_function.invoke_arn
}

# Disconnect integration
resource "aws_apigatewayv2_integration" "disconnect" {
  api_id           = aws_apigatewayv2_api.websocket.id
  integration_type = "AWS_PROXY"
  integration_uri  = var.connection_handler_function.invoke_arn
}

# Message routing integration
resource "aws_apigatewayv2_integration" "message" {
  api_id           = aws_apigatewayv2_api.websocket.id
  integration_type = "AWS_PROXY"
  integration_uri  = var.message_router_function.invoke_arn
}

# Lambda permissions for WebSocket API Gateway
resource "aws_lambda_permission" "websocket_connect" {
  statement_id  = "AllowWebSocketAPIGatewayInvokeConnect"
  action        = "lambda:InvokeFunction"
  function_name = var.connection_handler_function.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_apigatewayv2_api.websocket.execution_arn}/*/*"
}

resource "aws_lambda_permission" "websocket_disconnect" {
  statement_id  = "AllowWebSocketAPIGatewayInvokeDisconnect"
  action        = "lambda:InvokeFunction"
  function_name = var.connection_handler_function.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_apigatewayv2_api.websocket.execution_arn}/*/*"
}

resource "aws_lambda_permission" "websocket_message" {
  statement_id  = "AllowWebSocketAPIGatewayInvokeMessage"
  action        = "lambda:InvokeFunction"
  function_name = var.message_router_function.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_apigatewayv2_api.websocket.execution_arn}/*/*"
}

# SNS Topic for WebSocket message broadcasting
resource "aws_sns_topic" "websocket_messages" {
  name = "${local.name_prefix}-websocket-messages"

  # Enable delivery status logging for dev and staging
  lambda_success_feedback_role_arn    = var.environment != "production" ? aws_iam_role.sns_delivery_status[0].arn : null
  lambda_success_feedback_sample_rate = var.environment != "production" ? 100 : null
  lambda_failure_feedback_role_arn    = var.environment != "production" ? aws_iam_role.sns_delivery_status[0].arn : null

  # Enable active tracing for dev
  tracing_config = var.environment == "development" ? "Active" : "PassThrough"

  tags = merge(var.tags, {
    Name = "${local.name_prefix}-websocket-messages"
    Type = "WebSocket Message Broadcasting"
  })
}

# Dead Letter Queue for failed WebSocket messages
resource "aws_sqs_queue" "websocket_dlq" {
  name = "${local.name_prefix}-websocket-dlq"

  message_retention_seconds = 1209600 # 14 days
  visibility_timeout_seconds = 60

  tags = merge(var.tags, {
    Name = "${local.name_prefix}-websocket-dlq"
    Type = "WebSocket Dead Letter Queue"
  })
}

# Note: SNS subscription moved to main.tf to connect websocket-notifications topic
# The websocket-messages topic is reserved for internal WebSocket system messages

# IAM Role for SNS Delivery Status Logging (dev and staging only)
resource "aws_iam_role" "sns_delivery_status" {
  count = var.environment != "production" ? 1 : 0
  name  = "${local.name_prefix}-websocket-sns-delivery-status-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "sns.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

# IAM Policy for SNS Delivery Status Logging
resource "aws_iam_role_policy" "sns_delivery_status" {
  count = var.environment != "production" ? 1 : 0
  name  = "${local.name_prefix}-websocket-sns-delivery-status-policy"
  role  = aws_iam_role.sns_delivery_status[0].id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents",
          "logs:PutMetricFilter",
          "logs:PutRetentionPolicy"
        ]
        Resource = [
          "arn:aws:logs:*:*:log-group:/aws/sns/*"
        ]
      }
    ]
  })
}

# CloudWatch Log Group for SNS delivery status (dev and staging only)
resource "aws_cloudwatch_log_group" "sns_delivery_status" {
  count             = var.environment != "production" ? 1 : 0
  name              = "/aws/sns/${local.name_prefix}-websocket-delivery-status"
  retention_in_days = var.environment == "development" ? 7 : 14

  tags = merge(var.tags, {
    Name = "${local.name_prefix}-websocket-sns-delivery-status"
    Type = "SNS Delivery Logging"
  })
}

# CloudWatch Log Group for WebSocket API Gateway
resource "aws_cloudwatch_log_group" "websocket_api" {
  name              = "/aws/apigateway/${aws_apigatewayv2_api.websocket.name}"
  retention_in_days = var.is_production_or_staging ? 30 : 7

  tags = var.tags
}

# Enable logging for WebSocket API Gateway (only for development and staging)
# Production skips this due to API Gateway v2 account configuration requirements
resource "aws_apigatewayv2_stage" "websocket_logging" {
  count       = var.environment != "production" ? 1 : 0
  api_id      = aws_apigatewayv2_api.websocket.id
  name        = "${var.environment}-logging"
  auto_deploy = true

  access_log_settings {
    destination_arn = aws_cloudwatch_log_group.websocket_api.arn
    format = jsonencode({
      requestId      = "$context.requestId"
      ip             = "$context.identity.sourceIp"
      caller         = "$context.identity.caller"
      user           = "$context.identity.user"
      requestTime    = "$context.requestTime"
      routeKey       = "$context.routeKey"
      status         = "$context.status"
      error          = "$context.error.message"
      errorMessage   = "$context.error.messageString"
      connectionId   = "$context.connectionId"
    })
  }

  default_route_settings {
    throttling_rate_limit  = 1000
    throttling_burst_limit = 2000
    logging_level          = "INFO"
    data_trace_enabled     = var.environment == "development"
  }

  tags = var.tags
}