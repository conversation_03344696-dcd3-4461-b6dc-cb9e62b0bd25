import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';

/// Breadcrumb navigation component for Xbox media upload flow
///
/// Shows the user's progress through the Xbox media upload process:
/// Xbox Media → Edit → Upload → Post
class XboxFlowBreadcrumb extends StatelessWidget {
  final XboxFlowStep currentStep;
  final VoidCallback? onXboxMediaTap;
  final VoidCallback? onEditTap;
  final VoidCallback? onUploadTap;
  final VoidCallback? onPostTap;

  const XboxFlowBreadcrumb({
    super.key,
    required this.currentStep,
    this.onXboxMediaTap,
    this.onEditTap,
    this.onUploadTap,
    this.onPostTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: const BoxDecoration(
        color: AppColors.gfCardBackground,
        border: Border(
          bottom: BorderSide(color: AppColors.gfGrayBorder, width: 0.5),
        ),
      ),
      child: Row(
        children: [
          _buildStep(
            'Xbox Media',
            XboxFlowStep.xboxMedia,
            Icons.videogame_asset,
            onXboxMediaTap,
          ),
          _buildSeparator(),
          _buildStep('Edit', XboxFlowStep.edit, Icons.edit, onEditTap),
          _buildSeparator(),
          _buildStep(
            'Upload',
            XboxFlowStep.upload,
            Icons.cloud_upload,
            onUploadTap,
          ),
          _buildSeparator(),
          _buildStep('Post', XboxFlowStep.post, Icons.send, onPostTap),
        ],
      ),
    );
  }

  Widget _buildStep(
    String label,
    XboxFlowStep step,
    IconData icon,
    VoidCallback? onTap,
  ) {
    final isActive = step == currentStep;
    final isCompleted = step.index < currentStep.index;
    final isClickable = onTap != null && (isCompleted || isActive);

    return Expanded(
      child: GestureDetector(
        onTap: isClickable ? onTap : null,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Icon
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: _getStepColor(step),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: _getStepBorderColor(step),
                    width: 2,
                  ),
                ),
                child: Icon(
                  isCompleted ? Icons.check : icon,
                  size: 16,
                  color: _getStepIconColor(step),
                ),
              ),
              const SizedBox(height: 4),
              // Label
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
                  color: _getStepTextColor(step),
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSeparator() {
    return Container(
      width: 20,
      height: 2,
      margin: const EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        color: AppColors.gfGrayBorder,
        borderRadius: BorderRadius.circular(1),
      ),
    );
  }

  Color _getStepColor(XboxFlowStep step) {
    final isActive = step == currentStep;
    final isCompleted = step.index < currentStep.index;

    if (isCompleted) {
      return AppColors.gfGreen;
    } else if (isActive) {
      return AppColors.gfGreen.withValues(alpha: 0.2);
    } else {
      return Colors.transparent;
    }
  }

  Color _getStepBorderColor(XboxFlowStep step) {
    final isActive = step == currentStep;
    final isCompleted = step.index < currentStep.index;

    if (isCompleted || isActive) {
      return AppColors.gfGreen;
    } else {
      return AppColors.gfGrayBorder;
    }
  }

  Color _getStepIconColor(XboxFlowStep step) {
    final isActive = step == currentStep;
    final isCompleted = step.index < currentStep.index;

    if (isCompleted) {
      return Colors.black;
    } else if (isActive) {
      return AppColors.gfGreen;
    } else {
      return AppColors.gfGrayText;
    }
  }

  Color _getStepTextColor(XboxFlowStep step) {
    final isActive = step == currentStep;
    final isCompleted = step.index < currentStep.index;

    if (isCompleted || isActive) {
      return AppColors.gfOffWhite;
    } else {
      return AppColors.gfGrayText;
    }
  }
}

/// Enum representing the steps in the Xbox media upload flow
enum XboxFlowStep { xboxMedia, edit, upload, post }
