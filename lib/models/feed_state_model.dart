import 'dart:convert';
import 'post_model.dart';

/// Represents the cached state of a feed including posts, position, and metadata
class FeedState {
  final String feedType; // 'prime', 'followed', 'channel_{id}'
  final List<PostModel> posts;
  final int currentPostIndex;
  final double scrollOffset;
  final DateTime lastRefreshTime;
  final DateTime lastAccessTime;
  final int currentOffset; // For pagination
  final bool hasMore;
  final String? error;
  final Map<String, dynamic> metadata; // Additional feed-specific data

  const FeedState({
    required this.feedType,
    required this.posts,
    this.currentPostIndex = 0,
    this.scrollOffset = 0.0,
    required this.lastRefreshTime,
    required this.lastAccessTime,
    this.currentOffset = 0,
    this.hasMore = true,
    this.error,
    this.metadata = const {},
  });

  /// Create an empty feed state
  factory FeedState.empty(String feedType) {
    final now = DateTime.now();
    return FeedState(
      feedType: feedType,
      posts: [],
      currentPostIndex: 0,
      scrollOffset: 0.0,
      lastRefreshTime: now,
      lastAccessTime: now,
      currentOffset: 0,
      hasMore: true,
      error: null,
      metadata: {},
    );
  }

  /// Create a copy with updated values
  FeedState copyWith({
    String? feedType,
    List<PostModel>? posts,
    int? currentPostIndex,
    double? scrollOffset,
    DateTime? lastRefreshTime,
    DateTime? lastAccessTime,
    int? currentOffset,
    bool? hasMore,
    String? error,
    Map<String, dynamic>? metadata,
  }) {
    return FeedState(
      feedType: feedType ?? this.feedType,
      posts: posts ?? this.posts,
      currentPostIndex: currentPostIndex ?? this.currentPostIndex,
      scrollOffset: scrollOffset ?? this.scrollOffset,
      lastRefreshTime: lastRefreshTime ?? this.lastRefreshTime,
      lastAccessTime: lastAccessTime ?? this.lastAccessTime,
      currentOffset: currentOffset ?? this.currentOffset,
      hasMore: hasMore ?? this.hasMore,
      error: error ?? this.error,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Update access time to current time
  FeedState updateAccessTime() {
    return copyWith(lastAccessTime: DateTime.now());
  }

  /// Update refresh time to current time
  FeedState updateRefreshTime() {
    return copyWith(lastRefreshTime: DateTime.now());
  }

  /// Check if the feed data is stale and needs refresh
  bool isStale({Duration maxAge = const Duration(minutes: 5)}) {
    return DateTime.now().difference(lastRefreshTime) > maxAge;
  }

  /// Check if the feed was recently accessed
  bool isRecentlyAccessed({Duration maxAge = const Duration(minutes: 30)}) {
    return DateTime.now().difference(lastAccessTime) <= maxAge;
  }

  /// Convert to JSON for persistence
  Map<String, dynamic> toJson() {
    return {
      'feedType': feedType,
      'posts': posts.map((post) => post.toJson()).toList(),
      'currentPostIndex': currentPostIndex,
      'scrollOffset': scrollOffset,
      'lastRefreshTime': lastRefreshTime.toIso8601String(),
      'lastAccessTime': lastAccessTime.toIso8601String(),
      'currentOffset': currentOffset,
      'hasMore': hasMore,
      'error': error,
      'metadata': metadata,
    };
  }

  /// Create from JSON
  factory FeedState.fromJson(Map<String, dynamic> json) {
    return FeedState(
      feedType: json['feedType'] as String,
      posts: (json['posts'] as List<dynamic>?)
              ?.map((postJson) => PostModel.fromJson(postJson as Map<String, dynamic>))
              .toList() ??
          [],
      currentPostIndex: json['currentPostIndex'] as int? ?? 0,
      scrollOffset: (json['scrollOffset'] as num?)?.toDouble() ?? 0.0,
      lastRefreshTime: DateTime.parse(json['lastRefreshTime'] as String),
      lastAccessTime: DateTime.parse(json['lastAccessTime'] as String),
      currentOffset: json['currentOffset'] as int? ?? 0,
      hasMore: json['hasMore'] as bool? ?? true,
      error: json['error'] as String?,
      metadata: (json['metadata'] as Map<String, dynamic>?) ?? {},
    );
  }

  /// Convert to JSON string for storage
  String toJsonString() {
    return jsonEncode(toJson());
  }

  /// Create from JSON string
  factory FeedState.fromJsonString(String jsonString) {
    return FeedState.fromJson(jsonDecode(jsonString) as Map<String, dynamic>);
  }

  @override
  String toString() {
    return 'FeedState(feedType: $feedType, posts: ${posts.length}, '
        'currentPostIndex: $currentPostIndex, scrollOffset: $scrollOffset, '
        'lastRefreshTime: $lastRefreshTime, lastAccessTime: $lastAccessTime, '
        'currentOffset: $currentOffset, hasMore: $hasMore, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FeedState &&
        other.feedType == feedType &&
        other.posts.length == posts.length &&
        other.currentPostIndex == currentPostIndex &&
        other.scrollOffset == scrollOffset &&
        other.lastRefreshTime == lastRefreshTime &&
        other.lastAccessTime == lastAccessTime &&
        other.currentOffset == currentOffset &&
        other.hasMore == hasMore &&
        other.error == error;
  }

  @override
  int get hashCode {
    return Object.hash(
      feedType,
      posts.length,
      currentPostIndex,
      scrollOffset,
      lastRefreshTime,
      lastAccessTime,
      currentOffset,
      hasMore,
      error,
    );
  }
}

/// Cache statistics for monitoring and debugging
class FeedCacheStats {
  final int totalFeeds;
  final int totalPosts;
  final int totalMemoryUsage; // Approximate in bytes
  final DateTime lastCleanup;
  final Map<String, int> feedPostCounts;
  final Map<String, DateTime> feedLastAccess;

  const FeedCacheStats({
    required this.totalFeeds,
    required this.totalPosts,
    required this.totalMemoryUsage,
    required this.lastCleanup,
    required this.feedPostCounts,
    required this.feedLastAccess,
  });

  Map<String, dynamic> toJson() {
    return {
      'totalFeeds': totalFeeds,
      'totalPosts': totalPosts,
      'totalMemoryUsage': totalMemoryUsage,
      'lastCleanup': lastCleanup.toIso8601String(),
      'feedPostCounts': feedPostCounts,
      'feedLastAccess': feedLastAccess.map(
        (key, value) => MapEntry(key, value.toIso8601String()),
      ),
    };
  }
}
