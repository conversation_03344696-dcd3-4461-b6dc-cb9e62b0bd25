/// Twitch media item model for clips
class TwitchMediaItem {
  final String id;
  final TwitchMediaType type;
  final String title;
  final String thumbnailUrl;
  final String downloadUrl;
  final String embedUrl;
  final String broadcasterName;
  final String creatorName;
  final String gameId;
  final String language;
  final int viewCount;
  final DateTime createdAt;
  final int duration; // Duration in seconds
  final int? vodOffset; // Offset in the VOD, null if not available
  final String platform;

  TwitchMediaItem({
    required this.id,
    required this.type,
    required this.title,
    required this.thumbnailUrl,
    required this.downloadUrl,
    required this.embedUrl,
    required this.broadcasterName,
    required this.creatorName,
    required this.gameId,
    required this.language,
    required this.viewCount,
    required this.createdAt,
    required this.duration,
    this.vodOffset,
    required this.platform,
  });

  /// Create TwitchMediaItem from JSON
  factory TwitchMediaItem.fromJson(Map<String, dynamic> json) {
    return TwitchMediaItem(
      id: json['id'] as String,
      type: _parseMediaType(json['type'] as String),
      title: json['title'] as String? ?? '',
      thumbnailUrl: json['thumbnailUrl'] as String? ?? '',
      downloadUrl: json['downloadUrl'] as String? ?? '',
      embedUrl: json['embedUrl'] as String? ?? '',
      broadcasterName:
          json['broadcasterName'] as String? ?? 'Unknown Broadcaster',
      creatorName: json['creatorName'] as String? ?? 'Unknown Creator',
      gameId: json['gameId'] as String? ?? '',
      language: json['language'] as String? ?? 'en',
      viewCount: json['viewCount'] as int? ?? 0,
      createdAt: DateTime.parse(json['createdAt'] as String),
      duration: json['duration'] as int? ?? 0,
      vodOffset: json['vodOffset'] as int?,
      platform: json['platform'] as String? ?? 'Twitch',
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'thumbnailUrl': thumbnailUrl,
      'downloadUrl': downloadUrl,
      'embedUrl': embedUrl,
      'broadcasterName': broadcasterName,
      'creatorName': creatorName,
      'gameId': gameId,
      'language': language,
      'viewCount': viewCount,
      'createdAt': createdAt.toIso8601String(),
      'duration': duration,
      'vodOffset': vodOffset,
      'platform': platform,
    };
  }

  /// Parse media type from string
  static TwitchMediaType _parseMediaType(String typeString) {
    switch (typeString.toLowerCase()) {
      case 'clip':
        return TwitchMediaType.clip;
      default:
        return TwitchMediaType.clip;
    }
  }

  /// Get formatted duration string (e.g., "1:23")
  String get formattedDuration {
    final minutes = duration ~/ 60;
    final seconds = duration % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }

  /// Get formatted view count (e.g., "1.2K", "1.5M")
  String get formattedViewCount {
    if (viewCount >= 1000000) {
      return '${(viewCount / 1000000).toStringAsFixed(1)}M';
    } else if (viewCount >= 1000) {
      return '${(viewCount / 1000).toStringAsFixed(1)}K';
    } else {
      return viewCount.toString();
    }
  }

  /// Get file extension for download
  String get fileExtension {
    // Twitch clips are typically MP4 videos
    return '.mp4';
  }

  /// Get local filename for downloaded clip
  String get localFilename {
    // Create a safe filename from the clip title and ID
    final safeTitle = title.replaceAll(RegExp(r'[^\w\s-]'), '').trim();
    final truncatedTitle =
        safeTitle.length > 50 ? safeTitle.substring(0, 50) : safeTitle;
    return '${truncatedTitle}_$id$fileExtension';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TwitchMediaItem &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'TwitchMediaItem{id: $id, title: $title, broadcaster: $broadcasterName, duration: $formattedDuration}';
  }
}

/// Enum for Twitch media types
enum TwitchMediaType {
  clip;

  String get displayName {
    switch (this) {
      case TwitchMediaType.clip:
        return 'Clip';
    }
  }
}

/// Response model for Twitch media API calls
class TwitchMediaResponse {
  final List<TwitchMediaItem> items;
  final bool hasMore;
  final String? cursor;

  TwitchMediaResponse({
    required this.items,
    required this.hasMore,
    this.cursor,
  });

  factory TwitchMediaResponse.fromJson(Map<String, dynamic> json) {
    final clipsData = json['clips'] as List<dynamic>? ?? [];
    final clips =
        clipsData
            .map(
              (item) => TwitchMediaItem.fromJson(item as Map<String, dynamic>),
            )
            .toList();

    final pagination = json['pagination'] as Map<String, dynamic>? ?? {};

    return TwitchMediaResponse(
      items: clips,
      hasMore: pagination['hasMore'] as bool? ?? false,
      cursor: pagination['cursor'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'clips': items.map((item) => item.toJson()).toList(),
      'pagination': {'hasMore': hasMore, 'cursor': cursor},
    };
  }
}

/// Model for tracking download progress
class TwitchMediaDownloadProgress {
  final String mediaId;
  final double progress; // 0.0 to 1.0
  final int downloadedBytes;
  final int totalBytes;
  final String status; // 'downloading', 'completed', 'failed', 'paused'
  final String? error;

  TwitchMediaDownloadProgress({
    required this.mediaId,
    required this.progress,
    required this.downloadedBytes,
    required this.totalBytes,
    required this.status,
    this.error,
  });

  bool get isCompleted => status == 'completed';
  bool get isFailed => status == 'failed';
  bool get isDownloading => status == 'downloading';
  bool get isPaused => status == 'paused';

  /// Get formatted download size string
  String get formattedSize {
    final mb = totalBytes / (1024 * 1024);
    return '${mb.toStringAsFixed(1)} MB';
  }

  /// Get formatted progress percentage
  String get formattedProgress {
    return '${(progress * 100).toStringAsFixed(0)}%';
  }

  TwitchMediaDownloadProgress copyWith({
    String? mediaId,
    double? progress,
    int? downloadedBytes,
    int? totalBytes,
    String? status,
    String? error,
  }) {
    return TwitchMediaDownloadProgress(
      mediaId: mediaId ?? this.mediaId,
      progress: progress ?? this.progress,
      downloadedBytes: downloadedBytes ?? this.downloadedBytes,
      totalBytes: totalBytes ?? this.totalBytes,
      status: status ?? this.status,
      error: error ?? this.error,
    );
  }
}
