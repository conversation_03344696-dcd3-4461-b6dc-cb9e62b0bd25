import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:convert';
import '../services/feed_cache_manager.dart';
import '../services/app_lifecycle_manager.dart';
import '../theme/app_theme.dart';
import '../utils/app_logger.dart';

class CacheDebugScreen extends StatefulWidget {
  const CacheDebugScreen({super.key});

  @override
  State<CacheDebugScreen> createState() => _CacheDebugScreenState();
}

class _CacheDebugScreenState extends State<CacheDebugScreen> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Cache Debug',
          style: TextStyle(
            color: AppColors.gfGreen,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.gfDarkBlue,
        iconTheme: const IconThemeData(color: AppColors.gfOffWhite),
      ),
      backgroundColor: AppColors.gfDarkBackground,
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
              ),
            )
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildCacheStatsSection(),
                  const SizedBox(height: 24),
                  _buildLifecycleStatsSection(),
                  const SizedBox(height: 24),
                  _buildActionsSection(),
                ],
              ),
            ),
    );
  }

  Widget _buildCacheStatsSection() {
    return Card(
      color: AppColors.gfDarkBlue,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Cache Statistics',
              style: TextStyle(
                color: AppColors.gfGreen,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            FutureBuilder<Map<String, dynamic>>(
              future: _getCacheStats(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
                    ),
                  );
                }

                if (snapshot.hasError) {
                  return Text(
                    'Error: ${snapshot.error}',
                    style: const TextStyle(color: Colors.red),
                  );
                }

                final stats = snapshot.data ?? {};
                return Column(
                  children: stats.entries.map((entry) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            entry.key,
                            style: const TextStyle(
                              color: AppColors.gfOffWhite,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Text(
                            entry.value.toString(),
                            style: const TextStyle(
                              color: AppColors.gfGrayText,
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLifecycleStatsSection() {
    return Card(
      color: AppColors.gfDarkBlue,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'App Lifecycle Statistics',
              style: TextStyle(
                color: AppColors.gfGreen,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            FutureBuilder<Map<String, dynamic>>(
              future: _getLifecycleStats(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
                    ),
                  );
                }

                if (snapshot.hasError) {
                  return Text(
                    'Error: ${snapshot.error}',
                    style: const TextStyle(color: Colors.red),
                  );
                }

                final stats = snapshot.data ?? {};
                return Column(
                  children: stats.entries.map((entry) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            entry.key,
                            style: const TextStyle(
                              color: AppColors.gfOffWhite,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Text(
                            entry.value.toString(),
                            style: const TextStyle(
                              color: AppColors.gfGrayText,
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionsSection() {
    return Card(
      color: AppColors.gfDarkBlue,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Cache Actions',
              style: TextStyle(
                color: AppColors.gfGreen,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _clearAllCache,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Clear All Cache'),
              ),
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _clearPrimeCache,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.gfGrayText,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Clear Prime Feed Cache'),
              ),
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _clearFollowedCache,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.gfGrayText,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Clear Followed Feed Cache'),
              ),
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _exportCacheData,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.gfGreen,
                  foregroundColor: AppColors.gfDarkBlue,
                ),
                child: const Text('Export Cache Data'),
              ),
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _refreshStats,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.gfGreen,
                  foregroundColor: AppColors.gfDarkBlue,
                ),
                child: const Text('Refresh Stats'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<Map<String, dynamic>> _getCacheStats() async {
    try {
      final stats = FeedCacheManager.instance.getCacheStats();
      return {
        'Total Feeds': stats.totalFeeds,
        'Total Posts': stats.totalPosts,
        'Memory Usage (KB)': (stats.totalMemoryUsage / 1024).round(),
        'Last Cleanup': stats.lastCleanup.toString().split('.')[0],
        ...stats.feedPostCounts.map((key, value) => MapEntry('$key Posts', value)),
      };
    } catch (e) {
      AppLogger.error('CacheDebugScreen: Error getting cache stats: $e');
      return {'Error': e.toString()};
    }
  }

  Future<Map<String, dynamic>> _getLifecycleStats() async {
    try {
      final stats = AppLifecycleManager.instance.getLifecycleStats();
      return {
        'Current State': stats['currentState']?.toString().split('.').last ?? 'Unknown',
        'Is In Background': stats['isInBackground']?.toString() ?? 'Unknown',
        'Is In Foreground': stats['isInForeground']?.toString() ?? 'Unknown',
        'Background Duration (s)': stats['backgroundDuration']?.toString() ?? 'N/A',
        'Background Callbacks': stats['backgroundCallbacks']?.toString() ?? '0',
        'Foreground Callbacks': stats['foregroundCallbacks']?.toString() ?? '0',
      };
    } catch (e) {
      AppLogger.error('CacheDebugScreen: Error getting lifecycle stats: $e');
      return {'Error': e.toString()};
    }
  }

  Future<void> _clearAllCache() async {
    setState(() => _isLoading = true);
    try {
      await FeedCacheManager.instance.clearAllCache();
      _showSnackBar('All cache cleared successfully', Colors.green);
    } catch (e) {
      _showSnackBar('Error clearing cache: $e', Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _clearPrimeCache() async {
    setState(() => _isLoading = true);
    try {
      await FeedCacheManager.instance.clearFeedCache('prime');
      _showSnackBar('Prime feed cache cleared', Colors.green);
    } catch (e) {
      _showSnackBar('Error clearing prime cache: $e', Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _clearFollowedCache() async {
    setState(() => _isLoading = true);
    try {
      await FeedCacheManager.instance.clearFeedCache('followed');
      _showSnackBar('Followed feed cache cleared', Colors.green);
    } catch (e) {
      _showSnackBar('Error clearing followed cache: $e', Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _exportCacheData() async {
    try {
      final stats = FeedCacheManager.instance.getCacheStats();
      final lifecycleStats = AppLifecycleManager.instance.getLifecycleStats();
      
      final exportData = {
        'timestamp': DateTime.now().toIso8601String(),
        'cacheStats': stats.toJson(),
        'lifecycleStats': lifecycleStats,
      };

      final jsonString = const JsonEncoder.withIndent('  ').convert(exportData);
      await Clipboard.setData(ClipboardData(text: jsonString));
      _showSnackBar('Cache data copied to clipboard', Colors.green);
    } catch (e) {
      _showSnackBar('Error exporting cache data: $e', Colors.red);
    }
  }

  void _refreshStats() {
    setState(() {});
    _showSnackBar('Stats refreshed', Colors.green);
  }

  void _showSnackBar(String message, Color color) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: color,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }
}
