import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import '../utils/app_logger.dart';
import '../services/xbox_media_service.dart';
import '../services/twitch_media_service.dart';
import '../widgets/common/gf_button.dart';

class StorageManagementScreen extends StatefulWidget {
  const StorageManagementScreen({super.key});

  @override
  State<StorageManagementScreen> createState() =>
      _StorageManagementScreenState();
}

class _StorageManagementScreenState extends State<StorageManagementScreen> {
  bool _isLoading = true;
  int _xboxFilesCount = 0;
  int _twitchFilesCount = 0;
  int _xboxTotalSize = 0;
  int _twitchTotalSize = 0;
  List<Map<String, dynamic>> _xboxFiles = [];
  List<Map<String, dynamic>> _twitchFiles = [];

  @override
  void initState() {
    super.initState();
    _loadStorageInfo();
  }

  Future<void> _loadStorageInfo() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Load Xbox storage info
      final xboxFiles =
          await XboxMediaService.instance.getDownloadedMediaList();
      final xboxSize = await XboxMediaService.instance.getDownloadedMediaSize();

      // Load Twitch storage info
      final twitchFiles =
          await TwitchMediaService.instance.getDownloadedClipsList();
      final twitchSize =
          await TwitchMediaService.instance.getDownloadedClipsSize();

      if (mounted) {
        setState(() {
          _xboxFiles = xboxFiles;
          _twitchFiles = twitchFiles;
          _xboxFilesCount = xboxFiles.length;
          _twitchFilesCount = twitchFiles.length;
          _xboxTotalSize = xboxSize;
          _twitchTotalSize = twitchSize;
          _isLoading = false;
        });
      }
    } catch (e) {
      AppLogger.error('Error loading storage info', error: e);
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  Future<void> _deleteAllXboxFiles() async {
    final confirmed = await _showDeleteConfirmationDialog(
      'Delete All Xbox Files',
      'This will delete all downloaded Xbox screenshots and game clips. This action cannot be undone.',
    );

    if (confirmed) {
      try {
        final deletedCount =
            await XboxMediaService.instance.deleteAllDownloadedMedia();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Deleted $deletedCount Xbox files'),
              backgroundColor: AppColors.gfGreen,
            ),
          );
          _loadStorageInfo(); // Refresh the data
        }
      } catch (e) {
        AppLogger.error('Error deleting Xbox files', error: e);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to delete Xbox files'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _deleteAllTwitchFiles() async {
    final confirmed = await _showDeleteConfirmationDialog(
      'Delete All Twitch Clips',
      'This will delete all downloaded Twitch clips. This action cannot be undone.',
    );

    if (confirmed) {
      try {
        final deletedCount =
            await TwitchMediaService.instance.deleteAllDownloadedClips();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Deleted $deletedCount Twitch clips'),
              backgroundColor: AppColors.gfGreen,
            ),
          );
          _loadStorageInfo(); // Refresh the data
        }
      } catch (e) {
        AppLogger.error('Error deleting Twitch clips', error: e);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to delete Twitch clips'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<bool> _showDeleteConfirmationDialog(
    String title,
    String message,
  ) async {
    return await showDialog<bool>(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              backgroundColor: AppColors.gfDarkBackground,
              title: Text(
                title,
                style: const TextStyle(color: AppColors.gfOffWhite),
              ),
              content: Text(
                message,
                style: const TextStyle(color: AppColors.gfOffWhite),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text(
                    'Cancel',
                    style: TextStyle(color: AppColors.gfOffWhite),
                  ),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: const Text(
                    'Delete',
                    style: TextStyle(color: Colors.red),
                  ),
                ),
              ],
            );
          },
        ) ??
        false;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.gfDarkBackground,
      appBar: AppBar(
        backgroundColor: AppColors.gfDarkBackground,
        title: const Text(
          'Storage Management',
          style: TextStyle(color: AppColors.gfOffWhite),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.gfOffWhite),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: AppColors.gfOffWhite),
            onPressed: _loadStorageInfo,
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.gfGreen),
                ),
              )
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Storage Overview
                    _buildStorageOverview(),
                    const SizedBox(height: 24),

                    // Xbox Storage Section
                    _buildStorageSection(
                      title: 'Xbox Media',
                      filesCount: _xboxFilesCount,
                      totalSize: _xboxTotalSize,
                      onDeleteAll: _deleteAllXboxFiles,
                      files: _xboxFiles,
                      platform: 'xbox',
                    ),
                    const SizedBox(height: 24),

                    // Twitch Storage Section
                    _buildStorageSection(
                      title: 'Twitch Clips',
                      filesCount: _twitchFilesCount,
                      totalSize: _twitchTotalSize,
                      onDeleteAll: _deleteAllTwitchFiles,
                      files: _twitchFiles,
                      platform: 'twitch',
                    ),
                  ],
                ),
              ),
    );
  }

  Widget _buildStorageOverview() {
    final totalFiles = _xboxFilesCount + _twitchFilesCount;
    final totalSize = _xboxTotalSize + _twitchTotalSize;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.gfCardBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.gfGrayBorder),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Storage Overview',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.gfOffWhite,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '$totalFiles',
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: AppColors.gfGreen,
                    ),
                  ),
                  const Text(
                    'Total Files',
                    style: TextStyle(fontSize: 14, color: AppColors.gfGrayText),
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    _formatFileSize(totalSize),
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: AppColors.gfGreen,
                    ),
                  ),
                  const Text(
                    'Total Size',
                    style: TextStyle(fontSize: 14, color: AppColors.gfGrayText),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStorageSection({
    required String title,
    required int filesCount,
    required int totalSize,
    required VoidCallback onDeleteAll,
    required List<Map<String, dynamic>> files,
    required String platform,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.gfCardBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.gfGrayBorder),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.gfOffWhite,
                ),
              ),
              if (filesCount > 0)
                GFButton(
                  text: 'Delete All',
                  onPressed: onDeleteAll,
                  type: GFButtonType.danger,
                  height: 32,
                ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '$filesCount files',
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppColors.gfOffWhite,
                      ),
                    ),
                    Text(
                      _formatFileSize(totalSize),
                      style: const TextStyle(
                        fontSize: 12,
                        color: AppColors.gfGrayText,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (files.isNotEmpty) ...[
            const SizedBox(height: 16),
            const Text(
              'Recent Files:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.gfOffWhite,
              ),
            ),
            const SizedBox(height: 8),
            ...files.take(3).map((file) => _buildFileItem(file, platform)),
            if (files.length > 3)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  '... and ${files.length - 3} more files',
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppColors.gfGrayText,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
          ],
        ],
      ),
    );
  }

  Widget _buildFileItem(Map<String, dynamic> file, String platform) {
    final fileName = file['name'] as String;
    final fileSize = file['size'] as int;
    final fileType = file['type'] as String;

    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          Icon(
            fileType == 'video' ? Icons.videocam : Icons.image,
            size: 16,
            color: AppColors.gfGrayText,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              fileName,
              style: const TextStyle(fontSize: 12, color: AppColors.gfOffWhite),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Text(
            _formatFileSize(fileSize),
            style: const TextStyle(fontSize: 12, color: AppColors.gfGrayText),
          ),
        ],
      ),
    );
  }
}
