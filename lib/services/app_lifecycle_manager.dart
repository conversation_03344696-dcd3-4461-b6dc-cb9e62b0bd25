import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/widgets.dart';
import 'feed_cache_manager.dart';
import 'background_profile_cache_service.dart';
import '../utils/app_logger.dart';

/// Comprehensive app lifecycle manager that coordinates with feed cache and other services
class AppLifecycleManager with WidgetsBindingObserver {
  static final AppLifecycleManager _instance = AppLifecycleManager._internal();
  static AppLifecycleManager get instance => _instance;
  AppLifecycleManager._internal();

  // State tracking
  AppLifecycleState _currentState = AppLifecycleState.resumed;
  DateTime? _backgroundTime;
  DateTime? _foregroundTime;
  bool _isInitialized = false;

  // Callbacks for different lifecycle events
  final List<VoidCallback> _backgroundCallbacks = [];
  final List<VoidCallback> _foregroundCallbacks = [];
  final List<VoidCallback> _pauseCallbacks = [];
  final List<VoidCallback> _resumeCallbacks = [];

  // Configuration
  static const Duration _backgroundSaveDelay = Duration(seconds: 2);
  static const Duration _foregroundRestoreDelay = Duration(milliseconds: 500);

  // Timers
  Timer? _backgroundSaveTimer;
  Timer? _foregroundRestoreTimer;

  /// Current app lifecycle state
  AppLifecycleState get currentState => _currentState;

  /// Whether app is currently in background
  bool get isInBackground =>
      _currentState == AppLifecycleState.paused ||
      _currentState == AppLifecycleState.detached ||
      _currentState == AppLifecycleState.inactive;

  /// Whether app is currently in foreground
  bool get isInForeground => _currentState == AppLifecycleState.resumed;

  /// Time when app went to background
  DateTime? get backgroundTime => _backgroundTime;

  /// Time when app came to foreground
  DateTime? get foregroundTime => _foregroundTime;

  /// Duration app has been in background
  Duration? get backgroundDuration {
    if (_backgroundTime == null) return null;
    if (isInBackground) {
      return DateTime.now().difference(_backgroundTime!);
    }
    if (_foregroundTime != null) {
      return _foregroundTime!.difference(_backgroundTime!);
    }
    return null;
  }

  /// Initialize the lifecycle manager
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Add observer to WidgetsBinding
      WidgetsBinding.instance.addObserver(this);

      // Initialize feed cache manager
      await FeedCacheManager.instance.initialize();

      _isInitialized = true;
      _foregroundTime = DateTime.now();

      AppLogger.info('AppLifecycleManager: Initialized successfully');
      developer.log('AppLifecycleManager: Lifecycle management started');
    } catch (e, stackTrace) {
      AppLogger.error(
        'AppLifecycleManager: Failed to initialize',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// Add callback for when app goes to background
  void addBackgroundCallback(VoidCallback callback) {
    _backgroundCallbacks.add(callback);
  }

  /// Add callback for when app comes to foreground
  void addForegroundCallback(VoidCallback callback) {
    _foregroundCallbacks.add(callback);
  }

  /// Add callback for when app is paused
  void addPauseCallback(VoidCallback callback) {
    _pauseCallbacks.add(callback);
  }

  /// Add callback for when app is resumed
  void addResumeCallback(VoidCallback callback) {
    _resumeCallbacks.add(callback);
  }

  /// Remove callbacks
  void removeBackgroundCallback(VoidCallback callback) {
    _backgroundCallbacks.remove(callback);
  }

  void removeForegroundCallback(VoidCallback callback) {
    _foregroundCallbacks.remove(callback);
  }

  void removePauseCallback(VoidCallback callback) {
    _pauseCallbacks.remove(callback);
  }

  void removeResumeCallback(VoidCallback callback) {
    _resumeCallbacks.remove(callback);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    final previousState = _currentState;
    _currentState = state;

    AppLogger.info(
      'AppLifecycleManager: State changed from $previousState to $state',
    );
    developer.log('AppLifecycleManager: Lifecycle state: $state');

    switch (state) {
      case AppLifecycleState.paused:
        _handleAppPaused();
        break;
      case AppLifecycleState.resumed:
        _handleAppResumed();
        break;
      case AppLifecycleState.inactive:
        _handleAppInactive();
        break;
      case AppLifecycleState.detached:
        _handleAppDetached();
        break;
      case AppLifecycleState.hidden:
        _handleAppHidden();
        break;
    }
  }

  /// Handle app paused state
  void _handleAppPaused() {
    _backgroundTime = DateTime.now();
    _foregroundTime = null;

    // Cancel any pending foreground restore
    _foregroundRestoreTimer?.cancel();

    // Schedule background save with delay to avoid frequent saves
    _backgroundSaveTimer?.cancel();
    _backgroundSaveTimer = Timer(_backgroundSaveDelay, () {
      _performBackgroundSave();
    });

    // Notify background profile cache service
    BackgroundProfileCacheService.instance.onAppBackground();

    // Notify pause callbacks immediately
    for (final callback in _pauseCallbacks) {
      try {
        callback();
      } catch (e) {
        AppLogger.error('AppLifecycleManager: Error in pause callback: $e');
      }
    }

    AppLogger.debug(
      'AppLifecycleManager: App paused, scheduled background save',
    );
  }

  /// Handle app resumed state
  void _handleAppResumed() {
    final wasInBackground = _backgroundTime != null && _foregroundTime == null;
    _foregroundTime = DateTime.now();

    // Cancel background save if still pending
    _backgroundSaveTimer?.cancel();

    if (wasInBackground) {
      final backgroundDuration = this.backgroundDuration;
      AppLogger.info(
        'AppLifecycleManager: App resumed after ${backgroundDuration?.inSeconds ?? 0} seconds in background',
      );

      // Schedule foreground restore with delay to ensure UI is ready
      _foregroundRestoreTimer?.cancel();
      _foregroundRestoreTimer = Timer(_foregroundRestoreDelay, () {
        _performForegroundRestore();
      });

      // Notify background profile cache service of foreground
      BackgroundProfileCacheService.instance.onAppForeground();
    }

    // Notify background profile cache service of resume
    BackgroundProfileCacheService.instance.onAppResumed();

    // Notify resume callbacks
    for (final callback in _resumeCallbacks) {
      try {
        callback();
      } catch (e) {
        AppLogger.error('AppLifecycleManager: Error in resume callback: $e');
      }
    }
  }

  /// Handle app inactive state
  void _handleAppInactive() {
    // App is temporarily inactive (e.g., phone call, notification panel)
    // Don't trigger full background save yet
    AppLogger.debug('AppLifecycleManager: App inactive');
  }

  /// Handle app detached state
  void _handleAppDetached() {
    // App is being terminated
    _performBackgroundSave();
    AppLogger.debug('AppLifecycleManager: App detached, performing final save');
  }

  /// Handle app hidden state
  void _handleAppHidden() {
    // App is hidden but still running
    AppLogger.debug('AppLifecycleManager: App hidden');
  }

  /// Perform background save operations
  void _performBackgroundSave() {
    try {
      AppLogger.debug('AppLifecycleManager: Performing background save');

      // Notify background callbacks
      for (final callback in _backgroundCallbacks) {
        try {
          callback();
        } catch (e) {
          AppLogger.error(
            'AppLifecycleManager: Error in background callback: $e',
          );
        }
      }

      // Note: Feed cache is automatically saved when updated, so no explicit save needed
      AppLogger.debug('AppLifecycleManager: Background save completed');
    } catch (e, stackTrace) {
      AppLogger.error(
        'AppLifecycleManager: Error during background save',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// Perform foreground restore operations
  void _performForegroundRestore() {
    try {
      AppLogger.debug('AppLifecycleManager: Performing foreground restore');

      // Notify foreground callbacks
      for (final callback in _foregroundCallbacks) {
        try {
          callback();
        } catch (e) {
          AppLogger.error(
            'AppLifecycleManager: Error in foreground callback: $e',
          );
        }
      }

      AppLogger.debug('AppLifecycleManager: Foreground restore completed');
    } catch (e, stackTrace) {
      AppLogger.error(
        'AppLifecycleManager: Error during foreground restore',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// Get lifecycle statistics
  Map<String, dynamic> getLifecycleStats() {
    return {
      'currentState': _currentState.toString(),
      'isInBackground': isInBackground,
      'isInForeground': isInForeground,
      'backgroundTime': _backgroundTime?.toIso8601String(),
      'foregroundTime': _foregroundTime?.toIso8601String(),
      'backgroundDuration': backgroundDuration?.inSeconds,
      'backgroundCallbacks': _backgroundCallbacks.length,
      'foregroundCallbacks': _foregroundCallbacks.length,
      'pauseCallbacks': _pauseCallbacks.length,
      'resumeCallbacks': _resumeCallbacks.length,
    };
  }

  /// Dispose resources
  void dispose() {
    _backgroundSaveTimer?.cancel();
    _foregroundRestoreTimer?.cancel();

    WidgetsBinding.instance.removeObserver(this);

    _backgroundCallbacks.clear();
    _foregroundCallbacks.clear();
    _pauseCallbacks.clear();
    _resumeCallbacks.clear();

    _isInitialized = false;

    AppLogger.info('AppLifecycleManager: Disposed');
  }
}
