import 'dart:async';
import 'dart:developer' as developer;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/feed_state_model.dart';
import '../models/post_model.dart';
import '../utils/app_logger.dart';

/// Global feed cache manager for persistent state across app sessions
class FeedCacheManager {
  static final FeedCacheManager _instance = FeedCacheManager._internal();
  static FeedCacheManager get instance => _instance;
  FeedCacheManager._internal();

  // Cache storage
  final Map<String, FeedState> _memoryCache = {};
  SharedPreferences? _prefs;
  bool _isInitialized = false;

  // Cache configuration
  static const String _cacheKeyPrefix = 'feed_cache_';
  static const String _cacheStatsKey = 'feed_cache_stats';
  static const Duration _defaultMaxAge = Duration(minutes: 5);
  static const Duration _cleanupInterval = Duration(hours: 1);
  static const int _maxCachedFeeds = 10;
  static const int _maxPostsPerFeed = 100;

  // Cleanup timer
  Timer? _cleanupTimer;

  /// Initialize the cache manager
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadCacheFromStorage();
      _startCleanupTimer();
      _isInitialized = true;

      AppLogger.info('FeedCacheManager: Initialized successfully');
      developer.log(
        'FeedCacheManager: Cache initialized with ${_memoryCache.length} feeds',
      );
    } catch (e, stackTrace) {
      AppLogger.error(
        'FeedCacheManager: Failed to initialize',
        error: e,
        stackTrace: stackTrace,
      );
      _isInitialized = false;
    }
  }

  /// Get cached feed state
  FeedState? getCachedFeed(String feedType) {
    if (!_isInitialized) return null;

    final cached = _memoryCache[feedType];
    if (cached != null) {
      // Update access time
      _memoryCache[feedType] = cached.updateAccessTime();
      AppLogger.debug(
        'FeedCacheManager: Retrieved cached feed: $feedType with ${cached.posts.length} posts, currentPostIndex: ${cached.currentPostIndex}',
      );
      return _memoryCache[feedType];
    }

    AppLogger.debug('FeedCacheManager: No cached feed found for: $feedType');
    return null;
  }

  /// Cache feed state
  Future<void> cacheFeed(FeedState feedState) async {
    if (!_isInitialized) return;

    try {
      // Limit posts per feed to prevent memory issues
      final limitedPosts = feedState.posts.take(_maxPostsPerFeed).toList();
      final limitedFeedState = feedState.copyWith(posts: limitedPosts);

      _memoryCache[feedState.feedType] = limitedFeedState;
      await _saveFeedToStorage(limitedFeedState);

      AppLogger.debug(
        'FeedCacheManager: Cached feed ${feedState.feedType} with ${limitedPosts.length} posts',
      );

      // Cleanup if we have too many cached feeds
      if (_memoryCache.length > _maxCachedFeeds) {
        await _cleanupOldFeeds();
      }
    } catch (e, stackTrace) {
      AppLogger.error(
        'FeedCacheManager: Failed to cache feed ${feedState.feedType}',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// Update feed position
  Future<void> updateFeedPosition(
    String feedType,
    int postIndex, {
    double? scrollOffset,
  }) async {
    if (!_isInitialized) return;

    final cached = _memoryCache[feedType];
    if (cached != null) {
      final updated = cached.copyWith(
        currentPostIndex: postIndex,
        scrollOffset: scrollOffset ?? cached.scrollOffset,
        lastAccessTime: DateTime.now(),
      );

      _memoryCache[feedType] = updated;
      await _saveFeedToStorage(updated);

      AppLogger.debug(
        'FeedCacheManager: Updated position for $feedType to index $postIndex (scrollOffset: $scrollOffset)',
      );
    }
  }

  /// Update feed posts (for new posts, likes, etc.)
  Future<void> updateFeedPosts(String feedType, List<PostModel> posts) async {
    if (!_isInitialized) return;

    final cached = _memoryCache[feedType];
    if (cached != null) {
      final limitedPosts = posts.take(_maxPostsPerFeed).toList();
      final updated = cached.copyWith(
        posts: limitedPosts,
        lastRefreshTime: DateTime.now(),
        lastAccessTime: DateTime.now(),
      );

      _memoryCache[feedType] = updated;
      await _saveFeedToStorage(updated);

      AppLogger.debug(
        'FeedCacheManager: Updated posts for $feedType with ${limitedPosts.length} posts',
      );
    }
  }

  /// Check if feed should be refreshed
  bool shouldRefreshFeed(String feedType, {Duration? maxAge}) {
    final cached = _memoryCache[feedType];
    if (cached == null) return true;

    return cached.isStale(maxAge: maxAge ?? _defaultMaxAge);
  }

  /// Clear specific feed cache
  Future<void> clearFeedCache(String feedType) async {
    if (!_isInitialized) return;

    _memoryCache.remove(feedType);
    await _removeFeedFromStorage(feedType);

    AppLogger.debug('FeedCacheManager: Cleared cache for $feedType');
  }

  /// Clear all cache
  Future<void> clearAllCache() async {
    if (!_isInitialized) return;

    _memoryCache.clear();
    await _clearAllStorage();

    AppLogger.info('FeedCacheManager: Cleared all cache');
  }

  /// Get cache statistics
  FeedCacheStats getCacheStats() {
    final totalPosts = _memoryCache.values.fold<int>(
      0,
      (sum, feed) => sum + feed.posts.length,
    );

    final feedPostCounts = <String, int>{};
    final feedLastAccess = <String, DateTime>{};

    for (final entry in _memoryCache.entries) {
      feedPostCounts[entry.key] = entry.value.posts.length;
      feedLastAccess[entry.key] = entry.value.lastAccessTime;
    }

    // Rough memory usage calculation
    final memoryUsage = _memoryCache.values.fold<int>(
      0,
      (sum, feed) => sum + (feed.toJsonString().length * 2), // Rough estimate
    );

    return FeedCacheStats(
      totalFeeds: _memoryCache.length,
      totalPosts: totalPosts,
      totalMemoryUsage: memoryUsage,
      lastCleanup: DateTime.now(),
      feedPostCounts: feedPostCounts,
      feedLastAccess: feedLastAccess,
    );
  }

  /// Load cache from persistent storage
  Future<void> _loadCacheFromStorage() async {
    if (_prefs == null) return;

    try {
      final keys = _prefs!.getKeys().where(
        (key) => key.startsWith(_cacheKeyPrefix),
      );

      for (final key in keys) {
        final jsonString = _prefs!.getString(key);
        if (jsonString != null) {
          try {
            final feedState = FeedState.fromJsonString(jsonString);
            _memoryCache[feedState.feedType] = feedState;
          } catch (e) {
            AppLogger.error(
              'FeedCacheManager: Failed to parse cached feed from key $key: $e',
            );
            // Remove corrupted cache entry
            await _prefs!.remove(key);
          }
        }
      }

      AppLogger.info(
        'FeedCacheManager: Loaded ${_memoryCache.length} feeds from storage',
      );
    } catch (e, stackTrace) {
      AppLogger.error(
        'FeedCacheManager: Failed to load cache from storage',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// Save feed to persistent storage
  Future<void> _saveFeedToStorage(FeedState feedState) async {
    if (_prefs == null) return;

    try {
      final key = '$_cacheKeyPrefix${feedState.feedType}';
      await _prefs!.setString(key, feedState.toJsonString());
    } catch (e) {
      AppLogger.error(
        'FeedCacheManager: Failed to save feed ${feedState.feedType} to storage: $e',
      );
    }
  }

  /// Remove feed from persistent storage
  Future<void> _removeFeedFromStorage(String feedType) async {
    if (_prefs == null) return;

    try {
      final key = '$_cacheKeyPrefix$feedType';
      await _prefs!.remove(key);
    } catch (e) {
      AppLogger.error(
        'FeedCacheManager: Failed to remove feed $feedType from storage: $e',
      );
    }
  }

  /// Clear all persistent storage
  Future<void> _clearAllStorage() async {
    if (_prefs == null) return;

    try {
      final keys = _prefs!.getKeys().where(
        (key) => key.startsWith(_cacheKeyPrefix),
      );
      for (final key in keys) {
        await _prefs!.remove(key);
      }
      await _prefs!.remove(_cacheStatsKey);
    } catch (e) {
      AppLogger.error('FeedCacheManager: Failed to clear storage: $e');
    }
  }

  /// Start cleanup timer
  void _startCleanupTimer() {
    _cleanupTimer?.cancel();
    _cleanupTimer = Timer.periodic(_cleanupInterval, (_) => _cleanupOldFeeds());
  }

  /// Cleanup old and unused feeds
  Future<void> _cleanupOldFeeds() async {
    try {
      final feedsToRemove = <String>[];

      // Find feeds that haven't been accessed recently
      for (final entry in _memoryCache.entries) {
        if (!entry.value.isRecentlyAccessed()) {
          feedsToRemove.add(entry.key);
        }
      }

      // Remove old feeds
      for (final feedType in feedsToRemove) {
        await clearFeedCache(feedType);
      }

      if (feedsToRemove.isNotEmpty) {
        AppLogger.info(
          'FeedCacheManager: Cleaned up ${feedsToRemove.length} old feeds',
        );
      }
    } catch (e) {
      AppLogger.error('FeedCacheManager: Error during cleanup: $e');
    }
  }

  /// Dispose resources
  void dispose() {
    _cleanupTimer?.cancel();
    _memoryCache.clear();
    _isInitialized = false;
  }
}
