import 'dart:async';
import 'dart:developer' as developer;
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../models/twitch_media_model.dart';
import '../utils/app_logger.dart';
import 'api_service.dart';

/// Exception types for Twitch download errors
enum TwitchDownloadErrorType {
  networkError,
  invalidUrl,
  fileSystemError,
  serverError,
  timeout,
  unknown,
}

/// Custom exception for Twitch download errors
class TwitchDownloadException implements Exception {
  final String message;
  final TwitchDownloadErrorType type;
  final String? details;

  TwitchDownloadException(this.message, this.type, [this.details]);

  @override
  String toString() => 'TwitchDownloadException: $message';

  /// Get user-friendly error message
  String get userFriendlyMessage {
    switch (type) {
      case TwitchDownloadErrorType.networkError:
        return 'Network connection error. Please check your internet connection and try again.';
      case TwitchDownloadErrorType.invalidUrl:
        return 'Invalid download link. Please try refreshing your Twitch clips.';
      case TwitchDownloadErrorType.fileSystemError:
        return 'Unable to save file. Please check your device storage.';
      case TwitchDownloadErrorType.serverError:
        return 'Twitch server error. Please try again later.';
      case TwitchDownloadErrorType.timeout:
        return 'Download timed out. Please try again.';
      case TwitchDownloadErrorType.unknown:
        return 'An unexpected error occurred. Please try again.';
    }
  }
}

/// Service for fetching Twitch clips
class TwitchMediaService {
  static final TwitchMediaService _instance = TwitchMediaService._internal();
  static TwitchMediaService get instance => _instance;
  TwitchMediaService._internal();

  /// Get Twitch clips for the authenticated user
  Future<TwitchMediaResponse?> getClips({
    int first = 20,
    String? after,
    String? startedAt,
    String? endedAt,
  }) async {
    try {
      developer.log('TwitchMediaService: Fetching Twitch clips');
      AppLogger.info('Fetching Twitch clips');

      final queryParams = <String, String>{'first': first.toString()};

      if (after != null && after.isNotEmpty) {
        queryParams['after'] = after;
      }

      if (startedAt != null && startedAt.isNotEmpty) {
        queryParams['started_at'] = startedAt;
      }

      if (endedAt != null && endedAt.isNotEmpty) {
        queryParams['ended_at'] = endedAt;
      }

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'GET',
        path: '/twitch/clips',
        queryParams: queryParams,
      );

      if (response.statusCode == 200) {
        final data = ApiService.instance.parseResponse(response);
        developer.log(
          'TwitchMediaService: Successfully fetched ${data['clips']?.length ?? 0} clips',
        );
        AppLogger.info('Successfully fetched Twitch clips');

        return TwitchMediaResponse.fromJson(data);
      } else if (response.statusCode == 404) {
        developer.log('TwitchMediaService: No Twitch account linked');
        AppLogger.info('No Twitch account linked for clips');
        return null;
      } else {
        final data = ApiService.instance.parseResponse(response);
        developer.log(
          'TwitchMediaService: Failed to fetch clips: ${data['error']}',
        );
        AppLogger.error('Failed to fetch Twitch clips', error: data['error']);
        return null;
      }
    } catch (e) {
      developer.log('TwitchMediaService: Error fetching clips: $e');
      AppLogger.error('Error fetching Twitch clips', error: e);
      return null;
    }
  }

  /// Download progress tracking
  final Map<String, TwitchMediaDownloadProgress> _downloadProgress = {};
  final StreamController<TwitchMediaDownloadProgress> _progressController =
      StreamController<TwitchMediaDownloadProgress>.broadcast();

  /// Stream of download progress updates
  Stream<TwitchMediaDownloadProgress> get downloadProgressStream =>
      _progressController.stream;

  /// Get current download progress for a specific clip
  TwitchMediaDownloadProgress? getDownloadProgress(String clipId) {
    return _downloadProgress[clipId];
  }

  /// Check if a clip is currently downloading
  bool isDownloading(String clipId) {
    final progress = _downloadProgress[clipId];
    return progress?.isDownloading ?? false;
  }

  /// Check if a clip has been downloaded
  Future<bool> isDownloaded(String clipId) async {
    try {
      final file = await _getLocalFile(clipId);
      return await file.exists();
    } catch (e) {
      developer.log('TwitchMediaService: Error checking download status: $e');
      return false;
    }
  }

  /// Get local file path for a downloaded clip
  Future<File?> getLocalFile(String clipId) async {
    try {
      final file = await _getLocalFile(clipId);
      if (await file.exists()) {
        return file;
      }
      return null;
    } catch (e) {
      developer.log('TwitchMediaService: Error getting local file: $e');
      return null;
    }
  }

  /// Download a Twitch clip to local storage
  /// Note: Due to Twitch API limitations, this currently opens the clip page for manual download
  Future<File?> downloadClip(TwitchMediaItem clip) async {
    try {
      developer.log(
        'TwitchMediaService: Starting download for clip ${clip.id}',
      );
      AppLogger.info('Starting Twitch clip download for clip ${clip.id}');

      // Check if the download URL is a direct MP4 link or a Twitch page
      final isDirectMp4 =
          clip.downloadUrl.endsWith('.mp4') ||
          clip.downloadUrl.contains('clips-media-assets') ||
          clip.downloadUrl.contains('production.assets.clips.twitchcdn.net');

      if (!isDirectMp4) {
        // This is a Twitch page URL, not a direct download link
        // Due to Twitch API limitations, we need to open the page for manual download
        throw TwitchDownloadException(
          'Twitch clips require manual download. The clip page will open in your browser.',
          TwitchDownloadErrorType.invalidUrl,
          'Direct download not available due to Twitch API limitations',
        );
      }

      // Initialize progress tracking
      _downloadProgress[clip.id] = TwitchMediaDownloadProgress(
        mediaId: clip.id,
        progress: 0.0,
        downloadedBytes: 0,
        totalBytes: 0,
        status: 'downloading',
      );
      _progressController.add(_downloadProgress[clip.id]!);

      // Get local file path
      final localFile = await _getLocalFile(clip.id);

      // Check if already downloaded
      if (await localFile.exists()) {
        developer.log('TwitchMediaService: Clip ${clip.id} already downloaded');
        _downloadProgress[clip.id] = _downloadProgress[clip.id]!.copyWith(
          progress: 1.0,
          status: 'completed',
        );
        _progressController.add(_downloadProgress[clip.id]!);
        return localFile;
      }

      // Create directory if it doesn't exist
      await localFile.parent.create(recursive: true);

      // Download the clip
      final request = http.Request('GET', Uri.parse(clip.downloadUrl));
      final streamedResponse = await request.send();

      if (streamedResponse.statusCode != 200) {
        throw TwitchDownloadException(
          'Failed to download clip: HTTP ${streamedResponse.statusCode}',
          TwitchDownloadErrorType.serverError,
        );
      }

      final contentLength = streamedResponse.contentLength ?? 0;
      final sink = localFile.openWrite();
      int downloadedBytes = 0;

      await for (final chunk in streamedResponse.stream) {
        sink.add(chunk);
        downloadedBytes += chunk.length;

        // Update progress
        final progress =
            contentLength > 0 ? downloadedBytes / contentLength : 0.0;
        _downloadProgress[clip.id] = _downloadProgress[clip.id]!.copyWith(
          progress: progress,
          downloadedBytes: downloadedBytes,
          totalBytes: contentLength,
        );
        _progressController.add(_downloadProgress[clip.id]!);
      }

      await sink.close();

      // Mark as completed
      _downloadProgress[clip.id] = _downloadProgress[clip.id]!.copyWith(
        progress: 1.0,
        status: 'completed',
      );
      _progressController.add(_downloadProgress[clip.id]!);

      developer.log(
        'TwitchMediaService: Successfully downloaded clip ${clip.id}',
      );
      AppLogger.info('Successfully downloaded Twitch clip ${clip.id}');

      return localFile;
    } catch (e) {
      developer.log(
        'TwitchMediaService: Error downloading clip ${clip.id}: $e',
      );
      AppLogger.error('Error downloading Twitch clip ${clip.id}', error: e);

      // Mark as failed
      _downloadProgress[clip.id] = _downloadProgress[clip.id]!.copyWith(
        status: 'failed',
        error: e.toString(),
      );
      _progressController.add(_downloadProgress[clip.id]!);

      if (e is TwitchDownloadException) {
        rethrow;
      } else {
        throw TwitchDownloadException(
          'Failed to download clip: $e',
          TwitchDownloadErrorType.unknown,
          e.toString(),
        );
      }
    }
  }

  /// Get local file path for a clip
  Future<File> _getLocalFile(String clipId) async {
    final directory = await getApplicationDocumentsDirectory();
    final twitchDir = Directory(path.join(directory.path, 'twitch_clips'));
    return File(path.join(twitchDir.path, '$clipId.mp4'));
  }

  /// Delete a downloaded clip
  Future<bool> deleteClip(String clipId) async {
    try {
      final file = await _getLocalFile(clipId);
      if (await file.exists()) {
        await file.delete();
        developer.log('TwitchMediaService: Deleted clip $clipId');
        AppLogger.info('Deleted Twitch clip $clipId');
        return true;
      }
      return false;
    } catch (e) {
      developer.log('TwitchMediaService: Error deleting clip $clipId: $e');
      AppLogger.error('Error deleting Twitch clip $clipId', error: e);
      return false;
    }
  }

  /// Delete a downloaded clip by TwitchMediaItem
  Future<bool> deleteDownloadedClip(TwitchMediaItem clip) async {
    return await deleteClip(clip.id);
  }

  /// Delete all downloaded Twitch clips
  Future<int> deleteAllDownloadedClips() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final twitchDir = Directory(path.join(directory.path, 'twitch_clips'));

      if (!await twitchDir.exists()) {
        return 0;
      }

      final files = await twitchDir.list().toList();
      int deletedCount = 0;

      for (final file in files) {
        if (file is File && file.path.endsWith('.mp4')) {
          try {
            await file.delete();
            deletedCount++;
          } catch (e) {
            developer.log(
              'TwitchMediaService: Error deleting file ${file.path}: $e',
            );
          }
        }
      }

      developer.log('TwitchMediaService: Deleted $deletedCount Twitch clips');
      AppLogger.info('Deleted $deletedCount Twitch clips');
      return deletedCount;
    } catch (e) {
      developer.log(
        'TwitchMediaService: Error deleting all downloaded clips: $e',
      );
      AppLogger.error('Error deleting all Twitch clips', error: e);
      return 0;
    }
  }

  /// Get the total size of downloaded Twitch clips
  Future<int> getDownloadedClipsSize() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final twitchDir = Directory(path.join(directory.path, 'twitch_clips'));

      if (!await twitchDir.exists()) {
        return 0;
      }

      final files = await twitchDir.list().toList();
      int totalSize = 0;

      for (final file in files) {
        if (file is File && file.path.endsWith('.mp4')) {
          try {
            final stat = await file.stat();
            totalSize += stat.size;
          } catch (e) {
            developer.log(
              'TwitchMediaService: Error getting file size for ${file.path}: $e',
            );
          }
        }
      }

      return totalSize;
    } catch (e) {
      developer.log(
        'TwitchMediaService: Error calculating downloaded clips size: $e',
      );
      return 0;
    }
  }

  /// Get list of all downloaded Twitch clips with metadata
  Future<List<Map<String, dynamic>>> getDownloadedClipsList() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final twitchDir = Directory(path.join(directory.path, 'twitch_clips'));

      if (!await twitchDir.exists()) {
        return [];
      }

      final files = await twitchDir.list().toList();
      final List<Map<String, dynamic>> downloadedFiles = [];

      for (final file in files) {
        if (file is File && file.path.endsWith('.mp4')) {
          try {
            final stat = await file.stat();
            final fileName = path.basename(file.path);

            downloadedFiles.add({
              'path': file.path,
              'name': fileName,
              'size': stat.size,
              'modified': stat.modified,
              'type': 'video',
              'clipId': fileName.replaceAll('.mp4', ''),
            });
          } catch (e) {
            developer.log(
              'TwitchMediaService: Error getting file info for ${file.path}: $e',
            );
          }
        }
      }

      return downloadedFiles;
    } catch (e) {
      developer.log(
        'TwitchMediaService: Error getting downloaded clips list: $e',
      );
      return [];
    }
  }

  /// Check if a clip is downloaded locally
  Future<bool> isClipDownloaded(String clipId) async {
    try {
      final file = await _getLocalFile(clipId);
      return await file.exists();
    } catch (e) {
      return false;
    }
  }

  /// Clear all download progress tracking
  void clearProgress() {
    _downloadProgress.clear();
  }

  /// Dispose resources
  void dispose() {
    _progressController.close();
    _downloadProgress.clear();
  }
}
