import 'package:flutter_test/flutter_test.dart';
import 'package:gameflex_mobile/models/feed_state_model.dart';
import 'package:gameflex_mobile/models/post_model.dart';
import 'package:gameflex_mobile/services/feed_cache_manager.dart';

void main() {
  group('FeedCacheManager Tests', () {
    late FeedCacheManager cacheManager;

    setUp(() {
      cacheManager = FeedCacheManager.instance;
    });

    test('should create empty feed state', () {
      final feedState = FeedState.empty('test_feed');

      expect(feedState.feedType, equals('test_feed'));
      expect(feedState.posts, isEmpty);
      expect(feedState.currentPostIndex, equals(0));
      expect(feedState.scrollOffset, equals(0.0));
      expect(feedState.hasMore, isTrue);
    });

    test('should create feed state with posts', () {
      final posts = <PostModel>[
        PostModel(
          id: 'post1',
          userId: 'user1',
          username: 'testuser',
          displayName: 'Test User',
          content: 'Test content',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          likeCount: 0,
          commentCount: 0,
          reflexCount: 0,
          isActive: true,
          status: 'published',
          isLikedByCurrentUser: false,
        ),
      ];

      final feedState = FeedState(
        feedType: 'test_feed',
        posts: posts,
        currentPostIndex: 0,
        lastRefreshTime: DateTime.now(),
        lastAccessTime: DateTime.now(),
      );

      expect(feedState.posts.length, equals(1));
      expect(feedState.posts.first.id, equals('post1'));
    });

    test('should detect stale feed', () {
      final oldTime = DateTime.now().subtract(const Duration(minutes: 10));
      final feedState = FeedState(
        feedType: 'test_feed',
        posts: [],
        lastRefreshTime: oldTime,
        lastAccessTime: DateTime.now(),
      );

      expect(feedState.isStale(), isTrue);
      expect(feedState.isStale(maxAge: const Duration(minutes: 15)), isFalse);
    });

    test('should detect recently accessed feed', () {
      final recentTime = DateTime.now().subtract(const Duration(minutes: 5));
      final feedState = FeedState(
        feedType: 'test_feed',
        posts: [],
        lastRefreshTime: DateTime.now(),
        lastAccessTime: recentTime,
      );

      expect(feedState.isRecentlyAccessed(), isTrue);
      expect(
        feedState.isRecentlyAccessed(maxAge: const Duration(minutes: 2)),
        isFalse,
      );
    });

    test('should serialize and deserialize feed state', () {
      final posts = <PostModel>[
        PostModel(
          id: 'post1',
          userId: 'user1',
          username: 'testuser',
          displayName: 'Test User',
          content: 'Test content',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          likeCount: 5,
          commentCount: 2,
          reflexCount: 1,
          isActive: true,
          status: 'published',
          isLikedByCurrentUser: true,
        ),
      ];

      final originalFeedState = FeedState(
        feedType: 'test_feed',
        posts: posts,
        currentPostIndex: 0,
        lastRefreshTime: DateTime.now(),
        lastAccessTime: DateTime.now(),
        currentOffset: 1,
        hasMore: true,
      );

      final jsonString = originalFeedState.toJsonString();
      final deserializedFeedState = FeedState.fromJsonString(jsonString);

      expect(
        deserializedFeedState.feedType,
        equals(originalFeedState.feedType),
      );
      expect(
        deserializedFeedState.posts.length,
        equals(originalFeedState.posts.length),
      );
      expect(
        deserializedFeedState.posts.first.id,
        equals(originalFeedState.posts.first.id),
      );
      expect(
        deserializedFeedState.currentPostIndex,
        equals(originalFeedState.currentPostIndex),
      );
      expect(deserializedFeedState.hasMore, equals(originalFeedState.hasMore));
    });

    test('should update access time', () {
      final feedState = FeedState.empty('test_feed');
      final originalAccessTime = feedState.lastAccessTime;

      // Wait a bit to ensure time difference
      Future.delayed(const Duration(milliseconds: 10), () {
        final updatedFeedState = feedState.updateAccessTime();
        expect(
          updatedFeedState.lastAccessTime.isAfter(originalAccessTime),
          isTrue,
        );
      });
    });

    test('should copy with updated values', () {
      final originalFeedState = FeedState.empty('test_feed');
      final updatedFeedState = originalFeedState.copyWith(
        currentPostIndex: 5,
        scrollOffset: 100.0,
        hasMore: false,
      );

      expect(updatedFeedState.feedType, equals(originalFeedState.feedType));
      expect(updatedFeedState.currentPostIndex, equals(5));
      expect(updatedFeedState.scrollOffset, equals(100.0));
      expect(updatedFeedState.hasMore, isFalse);
    });
  });

  group('FeedCacheStats Tests', () {
    test('should create cache stats', () {
      final stats = FeedCacheStats(
        totalFeeds: 3,
        totalPosts: 50,
        totalMemoryUsage: 1024,
        lastCleanup: DateTime.now(),
        feedPostCounts: {'prime': 20, 'followed': 15, 'channel_1': 15},
        feedLastAccess: {'prime': DateTime.now(), 'followed': DateTime.now()},
      );

      expect(stats.totalFeeds, equals(3));
      expect(stats.totalPosts, equals(50));
      expect(stats.feedPostCounts['prime'], equals(20));
    });

    test('should serialize cache stats to JSON', () {
      final stats = FeedCacheStats(
        totalFeeds: 2,
        totalPosts: 30,
        totalMemoryUsage: 512,
        lastCleanup: DateTime.now(),
        feedPostCounts: {'prime': 20, 'followed': 10},
        feedLastAccess: {'prime': DateTime.now()},
      );

      final json = stats.toJson();
      expect(json['totalFeeds'], equals(2));
      expect(json['totalPosts'], equals(30));
      expect(json['feedPostCounts']['prime'], equals(20));
    });
  });
}
